---
description:
globs:
alwaysApply: true
---
1. UI 请使用 Compose
2. 当你需要编写 UI 的时候可以查看core-designsystem模块下component目录下或者core-ui模块下是否有合适的对应 UI 组件.
3. 如果在 Compose 中申请 Android 动态权限请使用rememberPermissionState,rememberLauncherForActivityResult相关类
4. 代码中涉及到的 UI 部分尽量不要使用高斯模糊代码
5. 所有在com.vocalbeats.designtoken.DesignIcons中出现的字符都需要使用`IconFontText`(位于core/designsystem/component/IconFont.kt)作为UI组件
6. 编写 Compose 界面一定要写Preview
7. 涉及到图片的请从 figma 下载图片放入指定资源文件夹,svg不要下载.
8. 设置颜色的时候一定要读取com.vocalbeats.designtoken包下的DesignColors，查看是否有已经定义好的颜色。如果定义好了请直接使用。
9. 字体样式请使用com.vocalbeats.designtoken包下的DesignFonts，IconFont使用com.vocalbeats.designtoken包下的DesignIcons
10. 比较通用的高度（比如topbar的高度）请优先查看core/designsystem/src/main/res/values/dimens.xml中是否有定义好的dimen,如果有请直接使用. 如果没有请在该文件中定义dimen,并使用
11. 在Compose中使用R.String.xx时不要用stringResource而是使用com.vocalbeats.scaffold.core.common.compose.ResourceKt.asString(int)
12. 在Compose中使用R.color.xx时不要用colorResource而是使用com.vocalbeats.scaffold.core.common.compose.ResourceKt.asColor(int)
13. 在Compose中使用R.drawable.xx时不要用painterResource而是使用com.vocalbeats.scaffold.core.common.compose.ResourceKt.asPainter(int)
14. 在Compose中使用R.dimen.xx时不要用dimensionResource而是使用com.vocalbeats.scaffold.core.common.compose.ResourceKt.asDimension(int)
15. 在Compose中，可点击按钮，请使用Box包裹Text，而不要使用Material包中的Button组件
16. 在Compose中，设置点击事件请使用com.vocalbeats.scaffold.core.common.compose.DebouncedKt.debouncedClickable而不是Modifier.clickable
17. 每个Compose方法都要包含modifier: Modifier = Modifier
18. 输入栏组件请使用
19. 不需要生成test相关的文件