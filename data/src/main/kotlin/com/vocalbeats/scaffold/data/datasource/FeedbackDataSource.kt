package com.vocalbeats.scaffold.data.datasource

import com.vocalbeats.grpclib.commom.RequestSubmitUserFeedback
import com.vocalbeats.grpclib.commom.ResponseSubmitUserFeedback
import com.vocalbeats.scaffold.core.network.Resp
import com.vocalbeats.scaffold.core.network.model.CommonServiceClient
import com.vocalbeats.scaffold.core.network.model.IDLCode
import javax.inject.Inject

class FeedbackDataSource @Inject constructor(
    private val client: CommonServiceClient
) {
    suspend fun feedback(description: String, email: String?): Resp<ResponseSubmitUserFeedback> {
        val request = RequestSubmitUserFeedback.newBuilder()
            .setContent(description)
            .setContact(email ?: "")  // 使用 email 参数
            .setScene("feedback") // 设置场景
            .build()

        return client.safetyCall {
            val result = submitUserFeedback(request)
           if (result.code == IDLCode.Success.code){
                Resp.Success(result)
            } else {
                Resp.Error(result.code, result.errMsg ?: "")
            }
        }
    }
}