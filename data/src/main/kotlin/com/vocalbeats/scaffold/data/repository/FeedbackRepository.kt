package com.vocalbeats.scaffold.data.repository

import com.vocalbeats.grpclib.commom.ResponseSubmitUserFeedback
import com.vocalbeats.scaffold.core.network.Resp
import com.vocalbeats.scaffold.data.datasource.FeedbackDataSource
import javax.inject.Inject

class FeedbackRepository @Inject constructor(
    private val dataSource: FeedbackDataSource
) {
     suspend fun feedback(description: String, email: String?): Resp<ResponseSubmitUserFeedback> {
         return dataSource.feedback(description, email)
     }
}