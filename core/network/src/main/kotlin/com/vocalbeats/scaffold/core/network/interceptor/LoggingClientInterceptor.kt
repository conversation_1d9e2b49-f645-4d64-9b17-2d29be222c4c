package com.vocalbeats.scaffold.core.network.interceptor

import com.vocalbeats.scaffold.core.common.ktx.logInfo
import io.grpc.*
import io.grpc.ForwardingClientCall.SimpleForwardingClientCall
import io.grpc.ForwardingClientCallListener.SimpleForwardingClientCallListener
import java.util.logging.Logger

class LoggingClientInterceptor : ClientInterceptor {
    private val TAG = "LoggingClientInterceptor"
    override fun <ReqT, RespT> interceptCall(
        method: MethodDescriptor<ReqT, RespT>,
        callOptions: CallOptions,
        next: Channel
    ): ClientCall<ReqT, RespT> {

        return object : SimpleForwardingClientCall<ReqT, RespT>(next.newCall(method, callOptions)) {

            override fun start(responseListener: Listener<RespT>, headers: Metadata) {
                logInfo(TAG,"=== gRPC 客户端请求开始 ===")
                logInfo(TAG,"方法: ${method.fullMethodName}")
                logInfo(TAG,"超时设置: ${callOptions.deadline}")
                logInfo(TAG,"请求头: ${formatMetadata(headers)}")

                val wrappedListener = object : SimpleForwardingClientCallListener<RespT>(responseListener) {
                    override fun onHeaders(headers: Metadata) {
                        logInfo(TAG,"收到响应头:  {formatMetadata(headers)}")
                        super.onHeaders(headers)
                    }

                    override fun onMessage(message: RespT) {
                        logInfo(TAG,"收到响应消息: $message")
                        super.onMessage(message)
                    }

                    override fun onClose(status: Status, trailers: Metadata) {
                        logInfo(TAG,"请求完成 - 状态: ${status.code}, 描述: ${status.description}")
                        logInfo(TAG,"响应尾部: ${formatMetadata(trailers)}")
                        logInfo(TAG,"=== gRPC 客户端请求结束 ===")
                        super.onClose(status, trailers)
                    }
                }

                super.start(wrappedListener, headers)
            }

            override fun sendMessage(message: ReqT) {
                logInfo(TAG,"发送请求消息: $message")
                super.sendMessage(message)
            }
        }
    }

    private fun formatMetadata(metadata: Metadata): String {
        val builder = StringBuilder()
        for (key in metadata.keys()) {
            if (key.endsWith(Metadata.BINARY_HEADER_SUFFIX)) {
                builder.append("$key: [二进制数据]\n")
            } else {
                val values = metadata.getAll(Metadata.Key.of(key, Metadata.ASCII_STRING_MARSHALLER))
                builder.append("$key: $values\n")
            }
        }
        return builder.toString()
    }
}