package com.vocalbeats.scaffold.core.network.model

import io.grpc.Status

/**
 * IDL错误码定义，映射gRPC Status Code
 * gRPC状态码从-10001开始映射
 */
enum class IDLCode(val code: Int) {
    // gRPC OK状态，表示成功
    Success(0),

    // gRPC状态码映射（从-10001开始）
    Cancelled(-10001),              // Status.Code.CANCELLED
    Unknown(-10002),                // Status.Code.UNKNOWN
    InvalidArgument(-10003),        // Status.Code.INVALID_ARGUMENT
    DeadlineExceeded(-10004),       // Status.Code.DEADLINE_EXCEEDED
    NotFound(-10005),               // Status.Code.NOT_FOUND
    AlreadyExists(-10006),          // Status.Code.ALREADY_EXISTS
    PermissionDenied(-10007),       // Status.Code.PERMISSION_DENIED
    ResourceExhausted(-10008),      // Status.Code.RESOURCE_EXHAUSTED
    FailedPrecondition(-10009),     // Status.Code.FAILED_PRECONDITION
    Aborted(-10010),                // Status.Code.ABORTED
    OutOfRange(-10011),             // Status.Code.OUT_OF_RANGE
    Unimplemented(-10012),          // Status.Code.UNIMPLEMENTED
    Internal(-10013),               // Status.Code.INTERNAL
    Unavailable(-10014),            // Status.Code.UNAVAILABLE
    DataLoss(-10015),               // Status.Code.DATA_LOSS
    Unauthenticated(-10016),       // Status.Code.UNAUTHENTICATED
    UnknowCrash(-20000);

    companion object {
        /**
         * 从gRPC Status.Code映射到IDLCode
         * @param statusCode gRPC状态码
         * @return 对应的IDLCode
         */
        fun fromGrpcStatusCode(statusCode: Status.Code): IDLCode {
            return when (statusCode) {
                Status.Code.OK -> Success
                Status.Code.CANCELLED -> Cancelled
                Status.Code.UNKNOWN -> Unknown
                Status.Code.INVALID_ARGUMENT -> InvalidArgument
                Status.Code.DEADLINE_EXCEEDED -> DeadlineExceeded
                Status.Code.NOT_FOUND -> NotFound
                Status.Code.ALREADY_EXISTS -> AlreadyExists
                Status.Code.PERMISSION_DENIED -> PermissionDenied
                Status.Code.RESOURCE_EXHAUSTED -> ResourceExhausted
                Status.Code.FAILED_PRECONDITION -> FailedPrecondition
                Status.Code.ABORTED -> Aborted
                Status.Code.OUT_OF_RANGE -> OutOfRange
                Status.Code.UNIMPLEMENTED -> Unimplemented
                Status.Code.INTERNAL -> Internal
                Status.Code.UNAVAILABLE -> Unavailable
                Status.Code.DATA_LOSS -> DataLoss
                Status.Code.UNAUTHENTICATED -> Unauthenticated
            }
        }

        /**
         * 从gRPC Status映射到IDLCode
         * @param status gRPC状态
         * @return 对应的IDLCode
         */
        fun fromGrpcStatus(status: Status): IDLCode {
            return fromGrpcStatusCode(status.code)
        }

        /**
         * 根据错误码获取IDLCode
         * @param code 错误码值
         * @return 对应的IDLCode，找不到时返回Unknown
         */
        fun fromCode(code: Int): IDLCode {
            return values().find { it.code == code } ?: Unknown
        }
    }
}