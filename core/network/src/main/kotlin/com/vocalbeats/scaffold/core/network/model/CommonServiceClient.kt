package com.vocalbeats.scaffold.core.network.model

import com.vocalbeats.grpclib.commom.CommonServiceGrpcKt
import com.vocalbeats.scaffold.core.network.Resp
import io.grpc.StatusException
import io.grpc.StatusRuntimeException
import javax.inject.Inject

class CommonServiceClient @Inject constructor(
    private val commonServiceCoroutineStub: CommonServiceGrpcKt.CommonServiceCoroutineStub
) {
    suspend fun <T> safetyCall(method: suspend CommonServiceGrpcKt.CommonServiceCoroutineStub.() -> Resp<T>): Resp<T> {
        return try {
            method.invoke(commonServiceCoroutineStub)
        }catch (e: Exception){
            when (e) {
                is StatusRuntimeException -> {
                    Resp.Error(IDLCode.fromGrpcStatusCode(e.status.code).code,e.message)
                }

                is StatusException -> {
                    Resp.Error(IDLCode.fromGrpcStatusCode(e.status.code).code,e.message)
                }

                else -> {
                    Resp.Error(IDLCode.UnknowCrash.code,e.message)
                }
            }
        }
    }
}