package com.vocalbeats.scaffold.core.network.interceptor

import io.grpc.CallOptions
import io.grpc.Channel
import io.grpc.ClientCall
import io.grpc.ClientInterceptor
import io.grpc.ForwardingClientCall
import io.grpc.Metadata
import io.grpc.MethodDescriptor

// TODO yangmulin 待确认请求头
class HeaderInterceptor : ClientInterceptor {
    override fun <ReqT, RespT> interceptCall(
        method: MethodDescriptor<ReqT, RespT>,
        callOptions: CallOptions,
        next: Channel
    ): ClientCall<ReqT, RespT> {
        return object : ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(
            next.newCall(method, callOptions)
        ) {
            override fun start(responseListener: Listener<RespT>, headers: Metadata) {
                // 添加全局 Header
                headers.put(
                    Metadata.Key.of("deviceId", Metadata.ASCII_STRING_MARSHALLER),
                    "android_device_id_test_111"// 动态获取 token 或其他 header 值
                )
                headers.put(
                    Metadata.Key.of("deviceType", Metadata.ASCII_STRING_MARSHALLER),
                    "android_mobile"
                )
                headers.put(
                    Metadata.Key.of("country", Metadata.ASCII_STRING_MARSHALLER),
                    "CN"
                )
                headers.put(
                    Metadata.Key.of("language", Metadata.ASCII_STRING_MARSHALLER),
                    "zh_CN"
                )
                headers.put(
                    Metadata.Key.of("x-real-ip", Metadata.ASCII_STRING_MARSHALLER),
                    "*******"
                )
                super.start(responseListener, headers)
            }
        }
    }
}