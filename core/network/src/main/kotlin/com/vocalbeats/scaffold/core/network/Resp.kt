package com.vocalbeats.scaffold.core.network

import android.app.VoiceInteractor

sealed interface Resp<T> {
    data class Error<T>(
        val code: Int?,
        val msg: String?,
    ) : Resp<T>, CancelableResp<T>

    data class Success<T>(
        val data: T,
    ) : Resp<T>, CancelableResp<T>
}

sealed interface CancelableResp<T> {
    data class Cancel<T>(val reason: String?) : CancelableResp<T>
}

//请求超时错误码
const val CODE_DEADLINE_EXCEEDED = -10001
const val CODE_CRASH = -10002
