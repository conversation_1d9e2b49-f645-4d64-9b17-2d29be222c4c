package com.vocalbeats.scaffold.core.network

import com.vocalbeats.grpclib.commom.CommonServiceGrpcKt
import com.vocalbeats.grpclib.commom.Head
import com.vocalbeats.scaffold.core.common.ktx.logInfo
import com.vocalbeats.scaffold.core.network.interceptor.DefaultTimeoutInterceptor
import com.vocalbeats.scaffold.core.network.interceptor.HeaderInterceptor
import com.vocalbeats.scaffold.core.network.interceptor.LoggingClientInterceptor
import io.grpc.CallOptions
import io.grpc.ConnectivityState
import io.grpc.ManagedChannelBuilder
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GrpcClient @Inject constructor() {
    private val TAG = "GrpcProvider"
    private val HOST = BuildConfig.idlHost
    private val PORT = BuildConfig.idlPort

    // private const val HOST = "**************"
    private val TIMEOUT_SECONDS = 5L
    private val KEEPALIVE_TIME_SECONDS = 60L
    private val KEEPALIVE_TIMEOUT_SECONDS = 10L

    private val channel by lazy {
        ManagedChannelBuilder.forAddress(HOST, PORT)
            .usePlaintext()
            .keepAliveTime(KEEPALIVE_TIME_SECONDS, TimeUnit.SECONDS)
            .keepAliveTimeout(KEEPALIVE_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .keepAliveWithoutCalls(true)
            .intercept( DefaultTimeoutInterceptor(10), LoggingClientInterceptor(),HeaderInterceptor())
            .build()
    }

    init {
        monitorChannelConnectState()
    }

    fun provideCommonCoroutineStub(): CommonServiceGrpcKt.CommonServiceCoroutineStub {
       return CommonServiceGrpcKt.CommonServiceCoroutineStub(channel)
    }

    //监听channel的连接状态
    private fun monitorChannelConnectState(){
        try {
            val currentState = channel.getState(false)
            logInfo(TAG, "gRPC Channel 当前连接状态: $currentState")
            
            // 监听状态变化
            channel.notifyWhenStateChanged(currentState) {
                val newState = channel.getState(false)
                logInfo(TAG, "gRPC Channel 状态已变更: $currentState -> $newState")
                
                when (newState) {
                    ConnectivityState.CONNECTING -> {
                        logInfo(TAG, "gRPC Channel 正在连接...")
                    }
                    ConnectivityState.READY -> {
                        logInfo(TAG, "gRPC Channel 连接就绪，可以发送请求")
                    }
                    ConnectivityState.TRANSIENT_FAILURE -> {
                        logInfo(TAG, "gRPC Channel 连接暂时失败，将尝试重连")
                    }
                    ConnectivityState.IDLE -> {
                        logInfo(TAG, "gRPC Channel 处于空闲状态")
                    }
                    ConnectivityState.SHUTDOWN -> {
                        logInfo(TAG, "gRPC Channel 已关闭")
                        return@notifyWhenStateChanged // 停止监听
                    }
                }
                
                // 递归监听下一次状态变化
                monitorChannelConnectState()
            }
        } catch (e: Exception) {
            logInfo(TAG, "监听 gRPC Channel 状态时发生异常: ${e.message}")
        }
    }

    /**
     * 关闭 gRPC Channel 并停止监听
     */
    fun shutdown() {
        try {
            if (!channel.isShutdown) {
                logInfo(TAG, "正在关闭 gRPC Channel...")
                channel.shutdown()
                // 等待最多 5 秒钟让 channel 优雅关闭
                if (!channel.awaitTermination(5, TimeUnit.SECONDS)) {
                    logInfo(TAG, "gRPC Channel 未在指定时间内关闭，强制关闭")
                    channel.shutdownNow()
                }
                logInfo(TAG, "gRPC Channel 已关闭")
            }
        } catch (e: Exception) {
            logInfo(TAG, "关闭 gRPC Channel 时发生异常: ${e.message}")
        }
    }

}