package com.vocalbeats.scaffold.core.network.interceptor

import io.grpc.CallOptions
import io.grpc.Channel
import io.grpc.ClientCall
import io.grpc.ClientInterceptor
import io.grpc.MethodDescriptor
import java.util.concurrent.TimeUnit

class DefaultTimeoutInterceptor(
    private val timeout: Long = 30,
    private val timeUnit: TimeUnit = TimeUnit.SECONDS
) : ClientInterceptor {

    override fun <ReqT, RespT> interceptCall(
        method: MethodDescriptor<ReqT, RespT>,
        callOptions: CallOptions,
        next: Channel
    ): ClientCall<ReqT, RespT> {

        val newCallOptions = if (callOptions.deadline == null) {
            callOptions.withDeadlineAfter(timeout, timeUnit)
        } else {
            callOptions
        }

        return next.newCall(method, newCallOptions)
    }
}