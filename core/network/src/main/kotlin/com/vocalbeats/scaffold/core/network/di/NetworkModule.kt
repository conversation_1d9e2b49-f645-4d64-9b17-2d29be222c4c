package com.vocalbeats.scaffold.core.network.di

import com.vocalbeats.grpclib.commom.CommonServiceGrpcKt
import com.vocalbeats.scaffold.core.network.GrpcClient
import com.vocalbeats.scaffold.core.network.model.CommonServiceClient
import com.vocalbeats.scaffold.core.network.monitor.ConnectivityManagerNetworkMonitor
import com.vocalbeats.scaffold.core.network.monitor.NetworkMonitor
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class NetworkModule {

    companion object {
        @Provides
        fun provideCommonServiceStub(provider: GrpcClient): CommonServiceGrpcKt.CommonServiceCoroutineStub{
            return provider.provideCommonCoroutineStub()
        }

        @Provides
        fun provideCommonServiceClient(stub: CommonServiceGrpcKt.CommonServiceCoroutineStub): CommonServiceClient {
            return CommonServiceClient(stub)
        }
    }

    @Binds
    abstract fun bindsNetworkMonitor(
        networkMonitor: ConnectivityManagerNetworkMonitor,
    ): NetworkMonitor
}
