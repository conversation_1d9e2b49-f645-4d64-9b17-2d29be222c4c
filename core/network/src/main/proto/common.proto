syntax = "proto2";

// 设置Android包名
option java_multiple_files = true;
option java_package = "com.vocalbeats.grpclib.commom";
// 设置前缀
option objc_class_prefix = "VB_";
option swift_prefix = "VB_";

package common;

// 公共服务
service CommonService {
  // 获取系统业务属性
  rpc getSystemBusinessProperties (RequestGetSystemBusinessProperties) returns (ResponseGetSystemBusinessProperties);
  // 提交用户反馈
  rpc submitUserFeedback (RequestSubmitUserFeedback) returns (ResponseSubmitUserFeedback);
}

// 获取系统业务属性请求
message RequestGetSystemBusinessProperties {
  // head 字段已移至 gRPC metadata
}

// 响应消息包含系统业务属性
message ResponseGetSystemBusinessProperties {
  repeated SystemBusinessProperty properties = 1;
  optional int32 code = 2;
  optional string errMsg = 3;
}

// 系统业务属性
message SystemBusinessProperty {
  optional string name = 1;
  optional string value = 2;
}

message Head {
  optional string deviceId = 1;
  optional string deviceType = 2;
  optional string country = 3;
  optional string language = 4;
}

// 提交用户反馈请求
message RequestSubmitUserFeedback {
  // head 字段已移至 gRPC metadata
  // 联系方式
  optional string contact = 1;
  // 反馈内容
  optional string content = 2;
  // 反馈场景 HELP_CENTER:帮助中心页；ASR_RESULT:ASR结果页; APP_RATING:应用评分页
  optional string scene = 3;
  // 传入该场景下的业务Id
  optional int64 bizId = 4;
  // 图片列表
  repeated string images = 5;
}

// 提交用户反馈响应
message ResponseSubmitUserFeedback {
  optional int32 code = 1;
  optional string errMsg = 2;
}