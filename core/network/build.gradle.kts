plugins {
    alias(libs.plugins.vocalbeats.android.library)
    alias(libs.plugins.vocalbeats.hilt)
    alias(libs.plugins.protobuf)
}

android {
    namespace = "${rootProject.ext["packageName"]}.core.network"

    sourceSets {
        named("main") {
            java {
                srcDirs("build/generated/source/proto/main/java")
            }
            kotlin {
                srcDirs("build/generated/source/proto/main/kotlin")
            }
        }
    }

    defaultConfig {
        buildConfigField("String", "idlHost", rootProject.ext["idlHost"] as String)
        buildConfigField("int", "idlPort", (rootProject.ext["idlPort"] as Int).toString())
    }

    buildFeatures {
        buildConfig = true
    }
}

protobuf {
    protoc {
        artifact = libs.protobuf.protoc.get().toString()
    }

    plugins {
        create("grpc") {
            artifact = libs.gen.grpc.java.get().toString()
        }
        create("grpckt") {
            artifact = libs.gen.grpc.kotlin.get().toString()
        }
    }

    generateProtoTasks {
        all().forEach {
            it.plugins {
                create("grpc") {
                    option("lite")
                }
                create("grpckt") {
                    option("lite")
                }
            }
            it.builtins {
                create("java") {
                    option("lite")
                }
                create("kotlin") {
                    option("lite")
                }
            }
        }
    }
}

dependencies {
    api(projects.core.common)

    // gRPC 核心依赖
    api(libs.grpc.okhttp)
    api(libs.grpc.protobuf.lite)
    api(libs.grpc.stub)
    api(libs.protobuf.kotlin.lite)
    api(libs.javax.annotation)
    api(libs.grpc.kotlin.stub)
    api(libs.protobuf.javalite)
}