package com.vocalbeats.scaffold.core.web.js

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import android.util.AttributeSet
import android.webkit.ValueCallback
import android.webkit.WebSettings
import com.vocalbeats.scaffold.core.common.ktx.logInfo
import com.yibasan.lizhifm.sdk.webview.*
import com.yibasan.lizhifm.sdk.webview.jswebview.LJavaScriptWebView

interface WebChromeClientCallBack {
    fun onReceivedTitle(view: LWebView?, title: String?)
}

/**
 * <AUTHOR>
 * @date 2022/8/10
 * @desc
 */
class JSWebView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : LJavaScriptWebView(context, attrs) {

    companion object {
        const val TAG = "WebViewJsBridge"
    }

    var webChromeClientCallBack: WebChromeClientCallBack? = null

    var fileChooserCallback: ((lc: ValueCallback<Array<Uri>>?, params: LFileChooserParams?) -> Unit)? =
        null

    init {
        initWebViewSetting()
        initWebChromeClient()
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initWebViewSetting() {
        /*WebView漏洞，移除该接口可避免恶意的代码注入*/
        if (BuildConfig.DEBUG) {
            setWebContentsDebuggingEnabled(true)
        }else{
            setWebContentsDebuggingEnabled(false)
        }
        settings.apply {
            setJavaScriptEnabled(true)
            setLoadsImagesAutomatically(true)
            setBlockNetworkImage(false)
            setLayoutAlgorithm(LWebSettings.LayoutAlgorithm.NARROW_COLUMNS)
            setSupportZoom(false)
            setBuiltInZoomControls(true)
            setUseWideViewPort(true)
            setDomStorageEnabled(true)
            setDatabaseEnabled(true)
            setCacheMode(WebSettings.LOAD_DEFAULT)
            setTextSize(LWebSettings.TextSize.NORMAL)
            setMediaPlaybackRequiresUserGesture(false)
            setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW)
            //在WebView中启用或禁用文件访问 Android11前默认为true，11后默认为false
            setAllowFileAccess(true)
            userAgentString = "$userAgentString BUZ"
        }
    }

    private fun initWebChromeClient() {
        setWebViewClient(object : LWebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: LWebView,
                request: LWebResourceRequest
            ): Boolean {
                return super.shouldOverrideUrlLoading(view, request)
            }
        })
        setWebChromeClient(object : LWebChromeClient() {
            override fun onReceivedTitle(view: LWebView?, title: String?) {
                super.onReceivedTitle(view, title)
                logInfo(TAG, "onReceivedTitle title:$title ")
                webChromeClientCallBack?.onReceivedTitle(view, title)
            }

            override fun onProgressChanged(view: LWebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                logInfo(TAG, "onProgressChanged newProgress:$newProgress ")
            }

            override fun onConsoleMessage(lConsoleMessage: LConsoleMessage?): Boolean {
                logInfo(TAG, "onConsoleMessage lConsoleMessage:$lConsoleMessage ")
                return super.onConsoleMessage(lConsoleMessage)
            }

            override fun onJsPrompt(
                view: LWebView?,
                url: String?,
                message: String?,
                defaultValue: String?,
                lResult: LJsPromptResult?
            ): Boolean {
                logInfo(TAG, "onJsPrompt message:$message ")
                return super.onJsPrompt(view, url, message, defaultValue, lResult)
            }

            override fun onJsConfirm(
                view: LWebView?,
                url: String?,
                message: String?,
                result: LJsResult?
            ): Boolean {
                logInfo(TAG, "onJsConfirm message:$message ")
                return super.onJsConfirm(view, url, message, result)
            }

            override fun onJsAlert(
                view: LWebView?,
                url: String?,
                message: String?,
                result: LJsResult?
            ): Boolean {
                logInfo(TAG, "onJsAlert message:$message ")
                return super.onJsAlert(view, url, message, result)
            }

            override fun onShowFileChooser(
                view: LWebView?,
                filePath: ValueCallback<Array<Uri>>?,
                params: LFileChooserParams?
            ): Boolean {
                logInfo(TAG, "onShowFileChooser filePath:$filePath ")
                fileChooserCallback?.invoke(filePath, params)
                return true
            }
        })
    }
}