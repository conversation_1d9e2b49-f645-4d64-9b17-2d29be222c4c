package com.vocalbeats.scaffold.core.web

import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.vocalbeats.designtoken.DesignColors
import com.vocalbeats.designtoken.DesignFonts
import com.vocalbeats.designtoken.DesignIcons
import com.vocalbeats.scaffold.core.ui.component.IconFontText
import com.vocalbeats.scaffold.core.web.js.JSFunctionData
import com.vocalbeats.scaffold.core.web.js.JSFunctionManager
import com.vocalbeats.scaffold.core.web.js.JSWebView
import com.yibasan.lizhifm.sdk.webview.jswebview.bean.JsCallbackDetail
import com.yibasan.lizhifm.sdk.webview.jswebview.jsbridge.JsBridgeMessageListener

/**
 * <AUTHOR>
 * @date 2025/8/13
 * @desc 通用 WebView 页面
 */
@Composable
fun WebViewScreen(
    url: String,
    title: String = "",
    onBackClick: () -> Unit = {},
    headers: Map<String, String> = emptyMap(),
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(DesignColors.white100)
            .statusBarsPadding()
    ) {
        var webView by remember { mutableStateOf<JSWebView?>(null) }
        val handleBackLambda = remember {
            {
                if (webView?.canGoBack() == true) {
                    webView?.goBack()
                } else {
                    onBackClick()
                }
            }
        }
        // 顶部导航栏
        WebViewTopBar(
            title = title,
            onBackClick = {
                handleBackLambda()
            },
            modifier = Modifier.fillMaxWidth()
        )

        val activity = LocalActivity.current as ComponentActivity

        BackHandler {
            handleBackLambda()
        }

        // WebView 内容区域
        AndroidView(
            factory = { context ->
                JSWebView(context).apply {
                    webView = this
                    // 加载 URL
                    if (headers.isNotEmpty()) {
                        loadUrl(url, headers.toMutableMap())
                    } else {
                        loadUrl(url)
                    }
                    jsBridgeMessageListener = object : JsBridgeMessageListener() {
                        /**
                         * 接收到 js --> Native 调用的方法
                         * 可以直接返回回调数据，
                         * 或者后续带上 [callbackId] 调用 JsWebView的 [triggerJsCallback] 方法进行回调
                         */
                        override fun receiveMessage(
                            method: String,
                            params: String,
                            callbackId: String
                        ): JsCallbackDetail? {
                            val data = JSFunctionData(method, params, callbackId)
                            return JSFunctionManager.invoke(activity, this@apply, data)
                        }
                    }
                }
            },
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        )

        DisposableEffect(Unit) {
            onDispose {
                webView?.apply {
                    // clearCache(true)
                    removeAllViews()
                    destroy()
                }
                webView = null
            }
        }
    }
}

/**
 * WebView 顶部导航栏组件
 */
@Composable
private fun WebViewTopBar(
    title: String,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .height(56.dp)
            .padding(horizontal = 20.dp),
        contentAlignment = Alignment.Center
    ) {
        // 返回按钮
        IconFontText(
            icon = DesignIcons.ArrowLeft,
            size = 24.dp,
            color = DesignColors.black80,
            onClick = onBackClick,
            modifier = Modifier.align(Alignment.CenterStart)
        )

        // 标题
        if (title.isNotEmpty()) {
            Text(
                text = title,
                style = DesignFonts.pjsMedium18(),
                color = DesignColors.black80,
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun WebViewScreenPreview() {
    WebViewScreen(
        url = "https://www.google.com",
        title = "Google",
        onBackClick = {}
    )
}