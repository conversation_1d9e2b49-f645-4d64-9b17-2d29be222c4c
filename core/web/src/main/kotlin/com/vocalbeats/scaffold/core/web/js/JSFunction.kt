package com.vocalbeats.scaffold.core.web.js

import androidx.activity.ComponentActivity
import com.yibasan.lizhifm.sdk.webview.jswebview.LJavaScriptWebView
import com.yibasan.lizhifm.sdk.webview.jswebview.bean.JsCallbackDetail
import org.json.JSONException
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date 2022/7/27
 * @desc
 */
data class JSFunctionData(val method: String, val params: String, val callbackId: String)

abstract class JSFunction {

    companion object{
        const val TAG = "JSFunction"
    }

    @Throws(JSONException::class)
    abstract operator fun invoke(
        activity: ComponentActivity,
        webView: LJavaScriptWebView,
        data: JSFunctionData
    ): JsCallbackDetail?

    fun optStringParam(data: JSFunctionData, key: String): String? {
        if (data.params.isEmpty()) return null
        val jsonParams = JSONObject(data.params)
        return jsonParams.optString(key)
    }

    fun optIntParam(data: JSFunctionData, key: String): Int? {
        if (data.params.isEmpty()) return null
        val jsonParams = JSONObject(data.params)
        if (jsonParams.has(key).not()) return null
        return jsonParams.optInt(key)
    }

    fun getFailedCallback(data: JSFunctionData) =
        JsCallbackDetail(data.callbackId).put("status", "failed")

    fun getSuccessCallback(data: JSFunctionData) =
        JsCallbackDetail(data.callbackId).put("status", "success")
}
