package com.vocalbeats.scaffold.core.web.js

import androidx.activity.ComponentActivity
import com.yibasan.lizhifm.sdk.webview.jswebview.LJavaScriptWebView
import com.yibasan.lizhifm.sdk.webview.jswebview.bean.JsCallbackDetail

/**
 * <AUTHOR>
 * @date 2025/8/13
 * @desc JS 函数管理器 - 简化版本
 */
object JSFunctionManager {
    private val FUNCTIONS = HashMap<String, Class<out JSFunction?>>()

    init {
        FUNCTIONS[GetAppInfoFunction.METHOD_NAME] = GetAppInfoFunction::class.java
        FUNCTIONS[CloseWebViewFunction.METHOD_NAME] = CloseWebViewFunction::class.java
    }
    fun queryExistFunction(functionName:String?):Boolean{
        if (functionName.isNullOrEmpty()) {
            return false
        }
        return FUNCTIONS.containsKey(functionName)
    }

    fun invoke(activity: ComponentActivity, webView: LJavaScriptWebView, data: JSFunctionData): JsCallbackDetail? {
        val callback = if (FUNCTIONS.containsKey(data.method)) {
            FUNCTIONS[data.method]?.newInstance()?.invoke(activity, webView, data)
        } else {
            null
        }
        return callback
    }
}