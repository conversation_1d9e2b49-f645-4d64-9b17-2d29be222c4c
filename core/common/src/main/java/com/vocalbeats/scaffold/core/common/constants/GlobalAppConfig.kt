package com.vocalbeats.scaffold.core.common.constants

import android.text.TextUtils
import com.yibasan.lizhi.identify.DeviceIdManger
import com.vocalbeats.scaffold.core.common.ktx.application
import androidx.core.os.ConfigurationCompat
import java.util.Locale

val deviceId: String
    get() = DeviceIdManger.deviceId

val channelId: String
    get() = "google"

val language: String
    get() {
        val locale = ConfigurationCompat.getLocales(application.resources.configuration)[0]
            ?: Locale.getDefault()
        return locale.language
    }

val country: String
    get() {
        val locale = ConfigurationCompat.getLocales(application.resources.configuration)[0]
            ?: Locale.getDefault()
        return locale.country
   }