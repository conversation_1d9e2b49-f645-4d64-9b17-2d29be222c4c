@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.ktx

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.WindowManager
import android.widget.Toast
import androidx.annotation.StringRes

fun toast(message: CharSequence?, context: Context = appContext) = showToast(message, context)

fun toast(@StringRes messageRes: Int, context: Context = appContext) = showToast(messageRes, context)

fun toastLong(message: CharSequence?, context: Context = appContext) =
    showToast(message, context, Toast.LENGTH_LONG)

fun toastLong(@StringRes messageRes: Int, context: Context = appContext) =
   showToast(messageRes, context, Toast.LENGTH_LONG)

fun toastDebug(message: CharSequence?, context: Context = appContext) =
    if (isDebug) toast(message, context) else Unit

fun toastDebug(@StringRes message: Int, context: Context = appContext) =
    if (isDebug) toast(message, context) else Unit

fun toastDebugLong(message: CharSequence?, context: Context = appContext) =
    if (isDebug) toastLong(message, context) else Unit

fun toastDebugLong(@StringRes message: Int, context: Context = appContext) =
    if (isDebug) toastLong(message, context) else Unit

private fun showToast(
    message: CharSequence?,
    context: Context,
    duration: Int = Toast.LENGTH_SHORT
) {
    Toast.makeText(context, message, duration).fixBadTokenException().show()
}

private fun showToast(
    @StringRes messageRes: Int,
    context: Context,
    duration: Int = Toast.LENGTH_SHORT
) {
    Toast.makeText(context, messageRes, duration).fixBadTokenException().show()
}

//修复7.1以上BadToken问题
fun Toast.fixBadTokenException() = apply {
    if (VERSION.SDK_INT == VERSION_CODES.N_MR1) {
        try {
            @SuppressLint("DiscouragedPrivateApi")
            val tnField = Toast::class.java.getDeclaredField("mTN")
            tnField.isAccessible = true
            val tn = tnField.get(this)

            val handlerField = tnField.type.getDeclaredField("mHandler")
            handlerField.isAccessible = true
            val handler = handlerField.get(tn) as Handler

            val looper = checkNotNull(Looper.myLooper()) {
                "Can't toast on a thread that has not called Looper.prepare()"
            }
            handlerField.set(tn, object : Handler(looper) {
                override fun handleMessage(msg: Message) {
                    try {
                        handler.handleMessage(msg)
                    } catch (ignored: WindowManager.BadTokenException) {
                    }
                }
            })
        } catch (ignored: IllegalAccessException) {
        } catch (ignored: NoSuchFieldException) {
        }
    }
}