@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.ktx

import android.R
import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isNotEmpty

inline val Activity.contentView: View?
    get() = (findViewById<View>(R.id.content) as ViewGroup).let {
        if (it.isNotEmpty()) it.getChildAt(0) else null
    }

val Context.activity: Activity?
    get() {
        var context: Context? = this
        while (context is ContextWrapper) {
            if (context is Activity) {
                return context
            }
            context = context.baseContext
        }
        return null
    }