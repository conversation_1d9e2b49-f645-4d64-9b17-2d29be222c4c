@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.compose

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import kotlin.math.roundToInt

@Composable
fun Dp.toInt() = this.value.roundToInt()

@Composable
fun Dp.dpToPx() = with(LocalDensity.current) { <EMAIL>() }

fun Dp.dpToPx(density: Density) = with(density) { <EMAIL>() }

@Composable
fun Dp.dpToSp() = with(LocalDensity.current) { <EMAIL>() }

@Composable
fun Dp.toPxInt() = with(LocalDensity.current) { <EMAIL>().roundToInt() }

@Composable
fun Int.pxToDp() = with(LocalDensity.current) { <EMAIL>() }

@Composable
fun Float.pxToDp() = with(LocalDensity.current) { <EMAIL>() }

@Composable
fun Int.textDp() = with(LocalDensity.current) { dp.toSp() }

@Composable
fun Float.textDp() = with(LocalDensity.current) { dp.toSp() }

@Composable
fun Dp.textSp() = with(LocalDensity.current) { toSp() }

@Composable
fun TextUnit.textDp() = with(LocalDensity.current) { toDp() }