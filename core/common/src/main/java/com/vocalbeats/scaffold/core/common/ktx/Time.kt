@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.ktx

import kotlin.math.ceil
import kotlin.math.roundToInt

const val millsOneSecond = 1000L
const val millsOneMinute = 1000L * 60
const val millsOneHour = 1000L * 60 * 60
const val millsOneDay = 1000L * 60 * 60 * 24
const val millsOneWeek = 1000L * 60 * 60 * 24 * 7
const val millsOneMonth = (1000 * 60 * 60 * 24 * (365.25f / 12f)).toLong()
const val millsOneYear = (1000 * 60 * 60 * 24 * 365.25f).toLong()

@JvmInline
value class Second(val value: Long) {
    val toFloat get() = value.toFloat()
}

fun Int.asSecond(): Second = Second(this.toLong())
fun Long.asSecond(): Second = Second(this)

@JvmInline
value class Millisecond(val value: Long) {
    val toFloat get() = value.toFloat()
    val toSecond get() = (value.toFloat() / 1000f).roundToInt()
    val ceilToSecond get() = ceil(value / 1000f).roundToInt()
}

fun Int.asMillisecond(): Millisecond = Millisecond(this.toLong())
fun Long.asMillisecond(): Millisecond = Millisecond(this)


/**
 * 格式化为"0:00"的时间格式
 */
fun Int.millisecondsToTime(): String {
    val seconds = this / 1000 % 60
    val minutes = this / 1000 / 60
    return "%2d:%02d".format(minutes, seconds).trim()
}
/**
 * 格式化为 "0:00" 的时间格式
 */
fun Int.secondsToTime(format: String = "%2d:%02d"): String {
    val seconds = this % 60
    val minutes = this / 60
    return format.format(minutes, seconds).trim()
}

/**
 * 格式化为"0:00"的时间格式
 */
fun Long.millisecondsToTime(): String {
    val seconds = this / 1000 % 60
    val minutes = this / 1000 / 60
    return "%2d:%02d".format(minutes, seconds).trim()
}

/**
 * 格式化为 "0:00" 的时间格式
 */
fun Long.secondsToTime(): String {
    val seconds = this % 60
    val minutes = this / 60
    return "%2d:%02d".format(minutes, seconds).trim()
}
