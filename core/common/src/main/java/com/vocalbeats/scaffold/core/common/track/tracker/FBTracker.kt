package com.vocalbeats.scaffold.core.common.track.tracker

import android.os.Bundle
import com.google.firebase.Firebase
import com.google.firebase.analytics.analytics


data class FBTrackInfo(
    val eventId: String,
    val params: Map<String, Any>,
)

fun reportFBPoint(fbTrackInfo: FBTrackInfo) {
    Firebase.analytics.logEvent(fbTrackInfo.eventId, fbTrackInfo.params.toFirebaseParams())
}

private fun Map<String,Any>.toFirebaseParams(): Bundle{
    val bundle = Bundle()
    forEach { (t, u) ->
        when (u) {
            is String -> {
                bundle.putString(t,u)
            }

            is Int -> {
                bundle.putInt(t,u)
            }

            is Long -> {
                bundle.putLong(t,u)
            }

            is Float -> {
                bundle.putFloat(t,u)
            }

            is Double -> {
                bundle.putDouble(t,u)
            }
        }
    }
    return bundle
}
