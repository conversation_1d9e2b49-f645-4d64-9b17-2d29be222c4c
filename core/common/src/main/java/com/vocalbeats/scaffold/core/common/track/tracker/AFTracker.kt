package com.vocalbeats.scaffold.core.common.track.tracker
import com.appsflyer.AppsFlyerLib
import com.vocalbeats.scaffold.core.common.ktx.application

data class AFTrackInfo(
    val eventId: String,
    val params: Map<String, Any>,
)

fun reportAFPoint(afTrackInfo: AFTrackInfo) {
    AppsFlyerLib.getInstance().logEvent(
        application,
        afTrackInfo.eventId,
        afTrackInfo.params
    )
}
