package com.vocalbeats.scaffold.core.common.track.tracker

import com.vocalbeats.scaffold.core.common.ktx.logDebug
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_BUSINESS_NUM
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_BUSINESS_TYPE
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_CARD_ID
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_CARD_TYPE
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_COLUMN
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_CONTENT_ID
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_DURATION
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_ELEMENT_BUSINESS_ID
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_ELEMENT_BUSINESS_TYPE
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_ELEMENT_COMMENT
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_EXCLUSIVE_ID
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_FAIL_SEASON
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_IS_SUCCESS
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_MENU
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_MKT_ID
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_MKT_NAME
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_MODULE
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_MODULE_ID
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_MODULE_NAME
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_PAGE_BUSINESS_ID
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_PAGE_BUSINESS_TYPE
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_PAGE_CONTENT
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_PAGE_STATUS
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_PLATFORM_TYPE
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_POSITION
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_REPORT_JSON
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_RESULT_TYPE
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_SOURCE
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_SUB_POSITION
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_TITLE
import com.vocalbeats.scaffold.core.common.track.constant.TrackConstant.KEY_VIEW_SOURCE
import com.yibasan.lizhi.tracker.LZTracker
import org.json.JSONException
import org.json.JSONObject

data class LZTrackInfo(
    val eventId: String,
    val params: HashMap<String, Any> = HashMap(),
    var postImmediate: Boolean = false
)

class LZTrackInfoBuilder(val eventId: String){
    val reportMap = HashMap<String, Any>()
    var postImmediate = false

    fun setCustomParam(key: String, value: Any): LZTrackInfoBuilder {
        reportMap[key] = value
        return this
    }

    fun setReportImmediate(isPostImmediate: Boolean): LZTrackInfoBuilder {
        this.postImmediate = isPostImmediate
        return this
    }


    fun <T> setMktName(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_MKT_NAME, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setExclusiveId(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_EXCLUSIVE_ID, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setDuration(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_DURATION, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }



    fun <T> setMktId(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_MKT_ID, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }


    fun setElementContent(elementContent: String?): LZTrackInfoBuilder {
        elementContent ?: return this

        try {
            setCustomParam(KEY_ELEMENT_COMMENT, elementContent)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun setTitle(title: String?): LZTrackInfoBuilder {
        title ?: return this

        try {
            setCustomParam(KEY_TITLE, title)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun setPageBusinessType(page_business_type: String?): LZTrackInfoBuilder {
        page_business_type ?: return this
        try {
            setCustomParam(KEY_PAGE_BUSINESS_TYPE, page_business_type)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setCartType(cartType: T?): LZTrackInfoBuilder {
        cartType ?: return this

        try {
            setCustomParam(KEY_CARD_TYPE, cartType)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setCartId(cartId: T?): LZTrackInfoBuilder {
        cartId ?: return this

        try {
            setCustomParam(KEY_CARD_ID, cartId)
        } catch (ignore: JSONException) {

        }
        return this
    }


    fun <T> setColumn(column: T?): LZTrackInfoBuilder {
        column ?: return this

        try {
            setCustomParam(KEY_COLUMN, column)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setPageBusinessId(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_PAGE_BUSINESS_ID, reportData)
        } catch (ignore: JSONException) {
        }
        return this
    }

    fun <T> setPageBusinessType(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_PAGE_BUSINESS_TYPE, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setPageStatus(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_PAGE_STATUS, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setMenu(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_MENU, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setElementBusinessType(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_ELEMENT_BUSINESS_TYPE, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setElementBusinessID(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_ELEMENT_BUSINESS_ID, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setElementBusinessContent(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_ELEMENT_BUSINESS_CONTENT, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setBusinessType(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_BUSINESS_TYPE, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setBusinessNum(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_BUSINESS_NUM, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setResultType(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_RESULT_TYPE, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setIsSuccess(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_IS_SUCCESS, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setFailReason(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_FAIL_SEASON, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setContentId(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_CONTENT_ID, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setPageContent(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_PAGE_CONTENT, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setModuleName(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_MODULE_NAME, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setModuleId(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_MODULE_ID, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setModule(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_MODULE, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setReportJson(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_REPORT_JSON, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setPosition(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_POSITION, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun <T> setSubPosition(reportData: T?): LZTrackInfoBuilder {
        reportData ?: return this

        try {
            setCustomParam(KEY_SUB_POSITION, reportData)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun setPlatformType(type: String): LZTrackInfoBuilder {
        try {
            setCustomParam(KEY_POSITION, type)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun setViewSource(type: String): LZTrackInfoBuilder {
        try {
            setCustomParam(KEY_VIEW_SOURCE, type)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun setSource(type: String): LZTrackInfoBuilder {
        try {
            setCustomParam(KEY_SOURCE, type)
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun setPlatformType(): LZTrackInfoBuilder {
        try {
            setCustomParam(KEY_PLATFORM_TYPE, "Android")
        } catch (ignore: JSONException) {

        }
        return this
    }

    fun build() = LZTrackInfo(eventId, reportMap, postImmediate)
}

fun reportLizhiPoint(lzTrackInfo: LZTrackInfo) {
    val jsonObject = JSONObject()

    lzTrackInfo.params.forEach { (t, u) ->
        jsonObject.put(t,u)
    }

    if (lzTrackInfo.postImmediate) {
        logDebug("OwllTracker-Immediate","${lzTrackInfo.eventId} - $jsonObject")
        LZTracker.trackImmediate(lzTrackInfo.eventId, jsonObject)
    } else {
        logDebug("OwllTracker-UnImmediate","${lzTrackInfo.eventId} - $jsonObject")
        LZTracker.track(lzTrackInfo.eventId, jsonObject)
    }

}