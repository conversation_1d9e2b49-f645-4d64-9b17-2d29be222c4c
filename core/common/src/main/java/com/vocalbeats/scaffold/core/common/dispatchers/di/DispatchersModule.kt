package com.vocalbeats.scaffold.core.common.dispatchers.di

import com.vocalbeats.scaffold.core.common.dispatchers.AppDispatchers.Default
import com.vocalbeats.scaffold.core.common.dispatchers.AppDispatchers.IO
import com.vocalbeats.scaffold.core.common.dispatchers.Dispatcher
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers

@Module
@InstallIn(SingletonComponent::class)
object DispatchersModule {
    @Provides
    @Dispatcher(IO)
    fun providesIODispatcher(): CoroutineDispatcher = Dispatchers.IO

    @Provides
    @Dispatcher(Default)
    fun providesDefaultDispatcher(): CoroutineDispatcher = Dispatchers.Default
}
