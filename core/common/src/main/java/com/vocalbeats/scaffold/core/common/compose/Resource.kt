@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.compose

import androidx.annotation.ColorInt
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource

@Composable
@ColorInt
fun Int.asColor() = colorResource(id = this)

@Composable
fun Int.asString() = stringResource(id = this)

@Composable
fun Int.asString(vararg formatArgs: Any) = stringResource(id = this, *formatArgs)

@Composable
fun Int.asDimension() = dimensionResource(id = this)

@Composable
fun Int.asPainter() = painterResource(id = this)