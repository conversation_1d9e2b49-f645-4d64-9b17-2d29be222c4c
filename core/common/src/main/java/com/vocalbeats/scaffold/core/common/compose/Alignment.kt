@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.compose

import androidx.compose.ui.Alignment

fun Alignment.Vertical.getBias() = when (this) {
    Alignment.Top -> -1f
    Alignment.CenterVertically -> 0f
    Alignment.Bottom -> 1f
    else -> 0f
}

fun Alignment.Horizontal.getBias() = when (this) {
    Alignment.Start -> -1f
    Alignment.CenterHorizontally -> 0f
    Alignment.End -> 1f
    else -> 0f
}