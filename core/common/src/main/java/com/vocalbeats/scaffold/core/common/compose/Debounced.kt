package com.vocalbeats.scaffold.core.common.compose

import android.os.SystemClock
import androidx.compose.foundation.clickable
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed

/**
 * <AUTHOR>
 * @date 2024/5/24
 * @desc
 */
const val DEFAULT_DEBOUNCE_TIME = 500L

@Composable
inline fun debounced(
    debounceTime: Long = DEFAULT_DEBOUNCE_TIME,
    crossinline onClick: () -> Unit
): () -> Unit {
    var lastTimeClicked by remember { mutableLongStateOf(0L) }
    val onClickLambda: () -> Unit = {
        val now = SystemClock.uptimeMillis()
        if (now - lastTimeClicked > debounceTime) {
            onClick()
            lastTimeClicked = now
        }
    }
    return onClickLambda
}

/**
 * The same as [Modifier.clickable] with support to debouncing.
 * @param withIndication 是否展示点击效果（水波纹或透明度变化）
 */
fun Modifier.debouncedClickable(
    enabled: Boolean = true,
    withIndication: Boolean = false,
    debounceTime: Long = DEFAULT_DEBOUNCE_TIME,
    onClick: () -> Unit
): Modifier {
    return this.composed {
        val clickable = debounced(debounceTime = debounceTime, onClick = { onClick() })
        if (withIndication) {
            this.clickable(
                enabled = enabled,
                onClick = clickable
            )
        } else {
            this.clickable(
                enabled = enabled,
                onClick = clickable,
                interactionSource = null,
                indication = null
            )
        }
    }
}