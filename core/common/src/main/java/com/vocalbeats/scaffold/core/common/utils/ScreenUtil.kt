package com.vocalbeats.scaffold.core.common.utils

import android.app.Activity
import android.content.Context
import android.content.res.Resources
import android.graphics.Insets
import android.graphics.Point
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.util.TypedValue
import android.view.View
import android.view.WindowInsets.Type
import android.view.WindowManager
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat

object ScreenUtil {

    private fun Context.getWindowManager(): WindowManager {
        return if (this is Activity) {
            windowManager
        } else {
            getSystemService(Context.WINDOW_SERVICE) as WindowManager
        }
    }

    /**
     * 获取屏宽
     */
    @JvmStatic
    fun getScreenWidth(context: Context): Int {
        return context.getWindowManager().getDisplayPointCompat(context).x
    }

    /**
     * 获取扣除导航栏和状态栏的高度
     */
    @JvmStatic
    fun getContentHeight(context: Context): Int {
        return getScreenHeight(context) - getStatusBarHeight(context) - getNavigationHeight(context)
    }

    /**
     * 获取屏高 不包括顶部状态栏和底部导航栏高度（比如手机真实总高度2960 状态栏150 导航栏50 则屏高为2760）
     */
    @JvmStatic
    fun getScreenHeight(context: Context): Int {
        return context.getWindowManager().getDisplayPointCompat(context).y
    }

    /**
     * 获取总屏宽 （基本和getScreenWidth()相同）
     */
    @JvmStatic
    fun getScreenWidthReal(context: Context): Int {
        return context.getWindowManager().getRealDisplayPointCompat(context).x
    }

    /**
     * 获取总屏高 包括顶部状态栏和底部导航栏高度（比如手机真实总高度2960）
     */
    @JvmStatic
    fun getScreenHeightReal(context: Context): Int {
        return context.getWindowManager().getRealDisplayPointCompat(context).y
    }

    /**
     * 获取状态栏高度
     */
    @JvmStatic
    fun getStatusBarHeight(context: Context): Int {
        return if (VERSION.SDK_INT >= VERSION_CODES.R) {
            val wm = context.getWindowManager()
            val windowMetrics = wm.currentWindowMetrics
            val windowInsets = windowMetrics.windowInsets
            val insets =
                windowInsets.getInsetsIgnoringVisibility(Type.systemBars() or Type.displayCutout())
            //添加兜底
            if (insets.top > 0) {
                insets.top
            } else {
                getStatusBarHeightByOldVersion(context)
            }
        } else {
            getStatusBarHeightByOldVersion(context)
        }
    }

    /**
     * 获取状态栏高度 旧版API
     */
    private fun getStatusBarHeightByOldVersion(context: Context): Int {
        val resourceId =
            context.resources.getIdentifier("status_bar_height", "dimen", "android")
        return context.resources.getDimensionPixelSize(resourceId)
    }

    /**
     * 获取导航栏高度
     */
    @JvmStatic
    fun getNavigationHeight(context: Context): Int {
        return if (VERSION.SDK_INT >= VERSION_CODES.R) {
            val wm = context.getWindowManager()
            val windowMetrics = wm.currentWindowMetrics
            val windowInsets = windowMetrics.windowInsets
            val insets =
                windowInsets.getInsetsIgnoringVisibility(Type.systemBars() or Type.displayCutout())
            //添加兜底
            if (insets.bottom > 0) {
                insets.bottom
            } else {
                getNavigationHeightByOldVersion(context)
            }
        } else {
            getNavigationHeightByOldVersion(context)
        }
    }

    /**
     * @param view: the view must has attached to window
     */
    fun getNavigationBarHeight(view: View): Int {
        val windowInsetsCompat = ViewCompat.getRootWindowInsets(view)
        val insets = windowInsetsCompat?.getInsets(WindowInsetsCompat.Type.navigationBars())
        return insets?.bottom ?: 0
    }

    /**
     * 获取屏幕宽度
     */
    @JvmStatic
    fun getDeviceWidth(context: Context): Int {
        return if (VERSION.SDK_INT >= VERSION_CODES.R) {
            context.getWindowManager().maximumWindowMetrics.bounds.width()
        } else {
            Resources.getSystem().displayMetrics.widthPixels
        }
    }

    /**
     * 获取屏幕高度
     */
    @JvmStatic
    fun getDeviceHeight(context: Context): Int {
        return if (VERSION.SDK_INT >= VERSION_CODES.R) {
            context.getWindowManager().maximumWindowMetrics.bounds.height()
        } else {
            Resources.getSystem().displayMetrics.heightPixels
        }
    }

    /**
     * 获取导航栏高度 旧版API
     */
    private fun getNavigationHeightByOldVersion(context: Context): Int {
        val resourceId =
            context.resources.getIdentifier("navigation_bar_height", "dimen", "android")
        return context.resources.getDimensionPixelSize(resourceId)
    }

    /**
     * 根据API获取屏幕宽高
     */
    private fun WindowManager.getDisplayPointCompat(context: Context): Point {
        //API 30新增windowInsets
        return if (VERSION.SDK_INT >= VERSION_CODES.R) {
            val windowInsets = currentWindowMetrics.windowInsets
            var insets: Insets = windowInsets.getInsets(Type.systemBars())
            windowInsets.displayCutout?.run {
                insets = Insets.max(
                    insets,
                    Insets.of(safeInsetLeft, safeInsetTop, safeInsetRight, safeInsetBottom)
                )
            }
            //insets的top为状态栏高度 bottom为导航栏高度
            val insetsWidth = insets.right + insets.left
            val insetsHeight = insets.top + insets.bottom
            //返回总高减去四边高度
            Point(
                currentWindowMetrics.bounds.width() - insetsWidth,
                currentWindowMetrics.bounds.height() - insetsHeight
            )
        } else {
            //API30及以上已被废弃
            Point().apply {
                context.display.getSize(this)
            }
        }
    }

    /**
     * 根据API获取屏幕真实总宽高
     */
    private fun WindowManager.getRealDisplayPointCompat(context: Context): Point {
        //API 30新增windowInsets
        return if (VERSION.SDK_INT >= VERSION_CODES.R) {
            val windowInsets = currentWindowMetrics.windowInsets
            var insets: Insets = windowInsets.getInsets(Type.systemBars())
            windowInsets.displayCutout?.run {
                insets = Insets.max(
                    insets,
                    Insets.of(safeInsetLeft, safeInsetTop, safeInsetRight, safeInsetBottom)
                )
            }
            //返回总宽高
            Point(
                currentWindowMetrics.bounds.width(),
                currentWindowMetrics.bounds.height()
            )
        } else {
            //API30及以上已被废弃
            Point().apply {
                context.display.getRealSize(this)
            }
        }
    }

    /**
     * 格式转换
     */
    private var density = -1f

    private fun getScreenDensity(context: Context): Float {
        if (density == -1f) {
            density = context.resources.displayMetrics.density
        }
        return density
    }

    @JvmStatic
    fun dp2px(dpValue: Float, context: Context): Int {
        return (dpValue * getScreenDensity(context) + 0.5f).toInt()
    }

    fun getSp(size: Float, context: Context): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_SP,
            size, context.resources.displayMetrics
        )
    }

    @JvmStatic
    fun dp2pxFloat(dpValue: Float, context: Context): Float {
        return dpValue * getScreenDensity(context)
    }

    @JvmStatic
    fun px2dp(pxValue: Float, context: Context): Int {
        return (pxValue / getScreenDensity(context) + 0.5f).toInt()
    }

    @JvmStatic
    fun px2dpFloat(pxValue: Float, context: Context): Float {
        return pxValue / getScreenDensity(context)
    }

    @JvmStatic
    fun sp2px(spValue: Float, context: Context): Int {
        return (spValue * context.resources.displayMetrics.scaledDensity + 0.5f).toInt()
    }

    @JvmStatic
    fun px2sp(pxValue: Float, context: Context): Int {
        return (pxValue / context.resources.displayMetrics.scaledDensity + 0.5f).toInt()
    }

}