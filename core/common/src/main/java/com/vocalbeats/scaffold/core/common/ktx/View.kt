@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.ktx

import android.graphics.Outline
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.annotation.ColorInt
import androidx.core.view.*
import com.vocalbeats.scaffold.core.common.utils.VibratorUtil
import com.vocalbeats.scaffold.core.common.R
import kotlin.math.abs
import kotlin.math.roundToInt
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

fun View.goneIf(goneIf: Boolean) = run { this.visibility = if (goneIf) View.GONE else View.VISIBLE }

fun View.visibleIf(showIf: Boolean) = run { this.visibility = if (showIf) View.VISIBLE else View.GONE }

fun View.inVisibleIf(inVisibleIf: Boolean) = run { this.visibility = if (inVisibleIf) View.INVISIBLE else View.VISIBLE }

fun View.gone() = run { this.visibility = View.GONE }

fun View.invisible() = run { this.visibility = View.INVISIBLE }

fun View.visible() = run { this.visibility = View.VISIBLE }

fun View.enable() = run { this.isEnabled = true }

fun View.disable() = run { this.isEnabled = false }

fun View.enableIf(enableIf: Boolean) = run { this.isEnabled = enableIf }

/**
 * 设置防双击点击事件
 * @param clickIntervals: 防双击间隔时长
 * @param isSharingIntervals: 是否与其它view共享间隔时长（点了viewA在间隔内能否点viewB）
 * */
fun View.click(
    clickIntervals: Long = 500L,
    isSharingIntervals: Boolean = false,
    vibrate: Boolean = false,
    parentView: View? = null,
    block: () -> Unit
) =
    setOnClickListener {
        val view = if (isSharingIntervals) parentView ?: context.activity?.window?.decorView ?: this else this
        val currentTime = System.currentTimeMillis()
        val lastTime = view.lastClickTime ?: 0L
        if (abs(currentTime - lastTime) > clickIntervals) {
            view.lastClickTime = currentTime
            if (vibrate) VibratorUtil.vibrator(context)
            block()
        }
    }

internal var View.lastClickTime: Long? by viewTags(R.id.core_common_tag_last_click_time)

inline fun View.longClick(crossinline block: () -> Unit) =
    setOnLongClickListener {
        block()
        true
    }

fun View.layoutSize(size: Int) =
    (parent as? ViewGroup)?.let {
        val lp = this.layoutParams
        lp.width = size
        lp.height = size
        this.layoutParams = lp
    }

fun View.layoutSize(width: Int, height: Int) =
    (parent as? ViewGroup)?.let {
        val lp = this.layoutParams
        lp.width = width
        lp.height = height
        this.layoutParams = lp
    }

fun View.layoutWidth(width: Int) =
    (parent as? ViewGroup)?.let {
        val lp = this.layoutParams
        lp.width = width
        this.layoutParams = lp
    }

fun View.layoutHeight(height: Int) =
    (parent as? ViewGroup)?.let {
        val lp = this.layoutParams
        lp.height = height
        this.layoutParams = lp
    }

fun View.layoutMargin(margin: Int) = layoutMargin(margin, margin, margin, margin)

fun View.layoutMargin(
    start: Int = marginStart,
    top: Int = marginTop,
    end: Int = marginEnd,
    bottom: Int = marginBottom
) =
    updateLayoutParams<ViewGroup.MarginLayoutParams> {
        updateMarginsRelative(start, top, end, bottom)
    }

fun View.layoutMarginHorizontal(margin: Int) =
    updateLayoutParams<ViewGroup.MarginLayoutParams> {
        updateMarginsRelative(margin, marginTop, margin, marginBottom)
    }

fun View.layoutMarginVertical(margin: Int) =
    updateLayoutParams<ViewGroup.MarginLayoutParams> {
        updateMarginsRelative(marginStart, margin, marginEnd, margin)
    }

fun View.layoutMarginStart(margin: Int) = layoutMargin(start = margin)
fun View.layoutMarginEnd(margin: Int) = layoutMargin(end = margin)
fun View.layoutMarginTop(margin: Int) = layoutMargin(top = margin)
fun View.layoutMarginBottom(margin: Int) = layoutMargin(bottom = margin)

fun View.layoutMarginStart(margin: Float) = layoutMargin(start = margin.roundToInt())
fun View.layoutMarginEnd(margin: Float) = layoutMargin(end = margin.roundToInt())
fun View.layoutMarginTop(margin: Float) = layoutMargin(top = margin.roundToInt())
fun View.layoutMarginBottom(margin: Float) = layoutMargin(bottom = margin.roundToInt())

fun View.layoutPadding(padding: Int) = layoutPadding(padding, padding, padding, padding)

fun View.layoutPadding(
    start: Int = paddingStart,
    top: Int = paddingTop,
    end: Int = paddingEnd,
    bottom: Int = paddingBottom
) =
    updateLayoutParams<ViewGroup.MarginLayoutParams> {
        updatePaddingRelative(start, top, end, bottom)
    }

fun View.layoutPaddingHorizontal(padding: Int) =
    updateLayoutParams<ViewGroup.MarginLayoutParams> {
        updatePaddingRelative(padding, paddingTop, padding, paddingBottom)
    }

fun View.layoutPaddingVertical(padding: Int) =
    updateLayoutParams<ViewGroup.MarginLayoutParams> {
        updatePaddingRelative(paddingStart, padding, paddingEnd, padding)
    }

fun View.layoutPaddingStart(padding: Int) = layoutPadding(start = padding)
fun View.layoutPaddingEnd(padding: Int) = layoutPadding(end = padding)
fun View.layoutPaddingTop(padding: Int) = layoutPadding(top = padding)
fun View.layoutPaddingBottom(padding: Int) = layoutPadding(bottom = padding)

fun View.layoutPaddingStart(padding: Float) = layoutPadding(start = padding.roundToInt())
fun View.layoutPaddingEnd(padding: Float) = layoutPadding(end = padding.roundToInt())
fun View.layoutPaddingTop(padding: Float) = layoutPadding(top = padding.roundToInt())
fun View.layoutPaddingBottom(padding: Float) = layoutPadding(bottom = padding.roundToInt())

fun View.backgroundColor(@ColorInt color: Int) {
    this.background.mutate().let {
        when (it) {
            //原本已设置背景的，直接修改背景色，其他属性会得以保留，比如圆角
            is GradientDrawable -> it.setColor(color)
            is ColorDrawable -> it.color = color
        }
    }
}

fun View.backgroundRadius(radius: Float) {
    this.background.mutate().let {
        when (it) {
            //原本已设置背景的，直接修改圆角，其他属性会得以保留，比如背景色
            is GradientDrawable -> it.cornerRadius = radius
        }
    }
}

/**
 * 修改四个角的圆角，需要传入长度为8的floatArray，格式如下
 * {topLeft, topLeft, topRight, topRight, bottomRight, bottomRight, bottomLeft, bottomLeft}
 */
fun View.backgroundRadii(radii: FloatArray) {
    this.background.mutate().let {
        when (it) {
            //原本已设置背景的，直接修改圆角，其他属性会得以保留，比如背景色
            is GradientDrawable -> it.cornerRadii = radii
        }
    }
}

fun View.backgroundGradient(@ColorInt color: Int, radius: Float) {
    background = GradientDrawable().apply {
        setColor(color)
        cornerRadius = radius
    }
}

fun View.setRadius(radius: Float) {
    clipToOutline = true
    outlineProvider = object : ViewOutlineProvider() {
        override fun getOutline(view: View, outline: Outline) {
            outline.setRoundRect(0, 0, view.width, view.height, radius)
        }
    }
}

fun View.setOval() {
    clipToOutline = true
    outlineProvider = object : ViewOutlineProvider() {
        override fun getOutline(view: View, outline: Outline) {
            val rect =  Rect(0, 0, view.width, view.height);
            outline.setRoundRect(rect, rect.width()/2f);
        }
    }
}

fun View.setTopRadius(radius: Float) {
    clipToOutline = true
    outlineProvider = object : ViewOutlineProvider() {
        override fun getOutline(view: View, outline: Outline) {
            outline.setRoundRect(0, 0, view.width, ((view.height + radius).toInt()), radius);

        }
    }
}

fun View?.isCompleteVisible(): Boolean{
    this?.let {
        val cover: Boolean
        val rect = Rect()
        cover = getGlobalVisibleRect(rect)
        if (cover) {
            if (rect.width() >= measuredWidth && rect.height() >= measuredHeight) {
                return true
            }
        }
    }
    return false
}

fun View?.isTouchedAt(x: Float, y: Float): Boolean =
    isTouchedAt(x.toInt(), y.toInt())

fun View?.isTouchedAt(x: Int, y: Int): Boolean =
    this?.locationOnScreenRect?.run { x in left..right && y in top..bottom } ?: false

fun View.findTouchedChild(x: Float, y: Float): View? =
    findTouchedChild(x.toInt(), y.toInt())

fun View.findTouchedChild(x: Int, y: Int): View? =
    touchables.find { it.isTouchedAt(x, y) }


inline val View.locationOnScreenRect: Rect
    get() = IntArray(2).let {
        getLocationOnScreen(it)
        Rect(it[0], it[1], it[0] + width, it[1] + height)
    }

inline val View.locationOnScreen: IntArray
    get() = IntArray(2).let {
        getLocationOnScreen(it)
        it
    }

fun <T> viewTags(key: Int) = object : ReadWriteProperty<View, T?> {
    @Suppress("UNCHECKED_CAST")
    override fun getValue(thisRef: View, property: KProperty<*>) =
        thisRef.getTag(key) as? T

    override fun setValue(thisRef: View, property: KProperty<*>, value: T?) =
        thisRef.setTag(key, value)
}

fun View.removeFromParent(){
    parent?.let {
        (it as? ViewGroup)?.removeView(this)
    }
}

inline fun View.doOnGlobalLayout(crossinline action: () -> Boolean) {
    viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
        override fun onGlobalLayout() {
            if (action.invoke()) {
                viewTreeObserver.removeOnGlobalLayoutListener(this)
            }
        }
    })
}