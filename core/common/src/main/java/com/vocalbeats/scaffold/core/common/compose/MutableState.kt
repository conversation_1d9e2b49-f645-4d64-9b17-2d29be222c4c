@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.compose

import androidx.compose.runtime.*

@Composable
fun rememberMutableBoolean(defaultValue: Boolean = false, key: Any? = null): MutableState<Boolean> =
    remember {
        mutableStateOf(defaultValue)
    }

@Composable
fun rememberMutableInt(defaultValue: Int = 0, key: Any? = null): MutableIntState = remember {
    mutableIntStateOf(defaultValue)
}

@Composable
fun rememberMutableFloat(defaultValue: Float = 0f, key: Any? = null): MutableFloatState =
    remember(key) {
        mutableFloatStateOf(defaultValue)
    }

@Composable
fun rememberMutableLong(defaultValue: Long = 0L, key: Any? = null): MutableLongState = remember {
    mutableLongStateOf(defaultValue)
}