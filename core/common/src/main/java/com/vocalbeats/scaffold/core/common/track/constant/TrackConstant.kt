package com.vocalbeats.scaffold.core.common.track.constant

object TrackConstant {
    const val EVENT_APP_CLICK = "\$AppClick"
    const val EVENT_VIEW_SCREEN = "ViewScreen"
    const val EVENT_APP_VIEW_SCREEN = "\$AppViewScreen"
    const val EVENT_CONTENT_EXPOSURE = "ContentExposure"
    const val EVENT_CONTENT_CLICK = "ContentClick"
    const val EVENT_MKT_EXPOSURE = "MktExposure"
    const val EVENT_MKT_CLICK = "MktClick"
    const val EVENT_RESULT_BACK = "ResultBack"
    const val EVENT_ENTER_PARTY = "EnterParty"
    const val EVENT_ELEMENT_EXPOSURE = "ElementExposure"

    const val KEY_ELEMENT_COMMENT = "\$element_content"
    const val KEY_CARD_TYPE = "card_type"
    const val KEY_CARD_ID = "card_id"
    const val KEY_COLUMN = "column"
    const val KEY_TITLE = "\$title"
    const val KEY_PAGE_BUSINESS_ID = "page_business_id"
    const val KEY_ELEMENT_BUSINESS_TYPE = "element_business_type"
    const val KEY_ELEMENT_BUSINESS_CONTENT = "element_business_content"
    const val KEY_PAGE_BUSINESS_TYPE = "page_business_type"
    const val KEY_RESULT_TYPE = "result_type"
    const val KEY_MODULE_NAME = "module_name"
    const val KEY_MODULE_ID = "module_id"
    const val KEY_POSITION = "position"
    const val KEY_BUSINESS_TYPE = "business_type"
    const val KEY_BUSINESS_NUM = "business_num"
    const val KEY_ELEMENT_BUSINESS_ID = "element_business_id"
    const val KEY_MKT_NAME = "mkt_name"
    const val KEY_MKT_ID = "mkt_id"
    const val KEY_PAGE_STATUS = "page_status"
    const val KEY_MODULE = "module"
    const val KEY_PLATFORM_TYPE = "platform_type"
    const val KEY_REPORT_JSON = "report_json"
    const val KEY_SUB_POSITION = "sub_position"
    const val KEY_MENU = "menu"
    const val KEY_VIEW_SOURCE = "view_source"
    const val KEY_SOURCE = "source"
    const val KEY_EXCLUSIVE_ID = "exclusive_id"
    const val KEY_IS_SUCCESS = "is_success"
    const val KEY_FAIL_SEASON = "fail_reason"
    const val KEY_CONTENT_ID = "content_id"
    const val KEY_PAGE_CONTENT = "page_content"
    const val KEY_DURATION = "duration"
}