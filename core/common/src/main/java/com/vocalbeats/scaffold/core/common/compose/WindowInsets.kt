@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.compose

import android.annotation.SuppressLint
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

@Composable
fun getScreenWidth(): Dp = LocalConfiguration.current.screenWidthDp.dp

@Composable
fun getScreenHeight(): Dp = LocalConfiguration.current.screenHeightDp.dp

@Composable
fun getStatusBarHeight() = with(LocalDensity.current) {
    WindowInsets.navigationBars.getTop(this).toDp()
}

@Composable
fun getNavigationBarHeight() = with(LocalDensity.current) {
    WindowInsets.navigationBars.getBottom(this).toDp()
}

@SuppressLint("WrongConstant")
@Composable
fun HideSystemBars(isShowNavigationBar: Boolean = false) {
    val activity = LocalActivity.current

    DisposableEffect(Unit) {
        val window = activity?.window ?: return@DisposableEffect onDispose {}
        val insetsController = WindowCompat.getInsetsController(window, window.decorView)

        val showLambda = {
            insetsController.apply {
                show(WindowInsetsCompat.Type.statusBars())
                show(WindowInsetsCompat.Type.navigationBars())
                systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_DEFAULT
            }
        }

        insetsController.apply {
            hide(WindowInsetsCompat.Type.statusBars())
            if (!isShowNavigationBar) {
                hide(WindowInsetsCompat.Type.navigationBars())
            }
            systemBarsBehavior =
                WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }

        onDispose {
            showLambda()
        }
    }
}

@SuppressLint("WrongConstant")
@Composable
fun ShowSystemBars() {
    val activity = LocalActivity.current
    val window = activity?.window ?: return
    val insetsController = WindowCompat.getInsetsController(window, window.decorView)
    insetsController.apply {
        show(WindowInsetsCompat.Type.statusBars())
        show(WindowInsetsCompat.Type.navigationBars())
        systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_DEFAULT
    }
}
