@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.ktx

import android.content.Context
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat

fun Context.isPermissionGranted(permission: String) =
    ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED

fun Context.isPermissionsGranted(permissions: List<String>): Boolean {
    for (permission in permissions) {
        if (!isPermissionGranted(permission)) {
            return false
        }
    }
    return true
}