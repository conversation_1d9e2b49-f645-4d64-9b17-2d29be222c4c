@file:Suppress("unused", "NOTHING_TO_INLINE")

package com.vocalbeats.scaffold.core.common.ktx

import android.util.Log

val Any.TAG_DEFAULT: String get() = javaClass.simpleName.limitLength(23)

inline val TAG_DEFAULT: String
    get() = "TAG_DEFAULT"

val StackTraceElement.isIgnorable: Boolean
    get() = isNativeMethod || className == Thread::class.java.name

val StackTraceElement.simpleClassName: String?
    get() = className.split(".").run {
        if (isNotEmpty()) last().limitLength(23) else null
    }

interface Logger {
    fun log(level: Int, tag: String, msg: String?, t: Throwable? = null, vararg args: Any?)
}

lateinit var appLogger: Logger

fun log(level: Int, tag: String, msg: String?, t: Throwable? = null, vararg args: Any?, logLine: LogLine? = null) =
    appLogger.log(level, logLine?.let { "$tag${logLine}" }?: tag, msg, t, args)

@JvmName("logKt")
inline fun Throwable?.log(tag: String) = log(Log.ERROR, tag, null, this)

@Deprecated(
    message = "Add TAG",
    replaceWith = ReplaceWith("logDebug(tag: String, msg: String?, vararg args: Any?)")
)
inline fun logDebug(msg: String?, vararg args: Any?) =
    log(Log.DEBUG, TAG_DEFAULT, msg, args = args)

@Deprecated(
    message = "Add TAG",
    replaceWith = ReplaceWith("logInfo(tag: String, msg: String?, vararg args: Any?)")
)
inline fun logInfo(msg: String?, vararg args: Any?, logLine: LogLine? = null) =
    log(Log.INFO, TAG_DEFAULT, msg, args = args, logLine = logLine)

@Deprecated(
    message = "Add TAG",
    replaceWith = ReplaceWith("logWarn(tag: String, msg: String?, vararg args: Any?)")
)
inline fun logWarn(msg: String?, vararg args: Any?, logLine: LogLine? = null) =
    log(Log.WARN, TAG_DEFAULT, msg, args = args, logLine = logLine)

@Deprecated(
    message = "Add TAG",
    replaceWith = ReplaceWith("logError(tag: String, msg: String?, vararg args: Any?)")
)
inline fun logError(msg: String?, vararg args: Any?, logLine: LogLine? = null) =
    log(Log.ERROR, TAG_DEFAULT, msg, args = args, logLine = logLine)

@Deprecated(
    message = "Add TAG",
    replaceWith = ReplaceWith("logError(tag: String, t: Throwable?, msg: String?, vararg args: Any?)")
)
inline fun logError(t: Throwable?, msg: String? = null, vararg args: Any?, logLine: LogLine? = null) =
    log(Log.ERROR, TAG_DEFAULT, msg, t, args, logLine)


/** 需要提供自定义tag */
fun log(tag: String, msg: String?) = log(Log.DEBUG, tag, msg)

fun log(tag: String, throwable: Throwable?, logLine: LogLine? = null) = log(Log.ERROR, tag, null, throwable, logLine = logLine)

fun String?.logByTag(tag: String) = log(Log.DEBUG, tag, this)

fun Throwable?.logByTag(tag: String, logLine: LogLine? = null) = log(Log.ERROR, tag, null, this, logLine = logLine)

fun Any?.logByTag(tag: String) = log(Log.DEBUG, tag, this.toString())

fun logVerbose(tag: String, msg: String?, vararg args: Any?) =
    log(Log.VERBOSE, tag, msg, args = args)

fun logVerbose(tag: String, t: Throwable?, msg: String? = null, vararg args: Any?) =
    log(Log.VERBOSE, tag, msg, t, args)

fun logDebug(tag: String, msg: String?, vararg args: Any?) =
    log(Log.DEBUG, tag, msg, args = args)

fun logDebug(tag: String, t: Throwable?, msg: String? = null, vararg args: Any?) =
    log(Log.DEBUG, tag, msg, t, args)

/**
 * @param logLine [LogLine]日志业务线，用于区分不同业务的日志，高效排查问题
 */
fun logInfo(tag: String, msg: String?, vararg args: Any?, logLine: LogLine? = null) =
    log(Log.INFO, tag, msg, args = args, logLine = logLine)

fun logLineInfo(tag: String, logLine: LogLine, msg: String?, vararg args: Any?) =
    log(Log.INFO, tag, msg, args = args, logLine = logLine)

fun logInfo(tag: String, t: Throwable?, msg: String? = null, vararg args: Any?, logLine: LogLine? = null) =
    log(Log.INFO, tag, msg, t, args, logLine)

fun logWarn(tag: String, msg: String?, vararg args: Any?, logLine: LogLine? = null) =
    log(Log.WARN, tag, msg, args = args, logLine = logLine)

fun logLineWarn(tag: String, logLine: LogLine, msg: String?, vararg args: Any?) =
    log(Log.WARN, tag, msg, args = args, logLine = logLine)

fun logWarn(tag: String, t: Throwable?, msg: String? = null, vararg args: Any?, logLine: LogLine? = null) =
    log(Log.WARN, tag, msg, t, args, logLine)

fun logError(tag: String, msg: String?, vararg args: Any?, logLine: LogLine? = null) =
    log(Log.ERROR, tag, msg, args = args, logLine = logLine)

fun logLineError(tag: String, logLine: LogLine, msg: String?, vararg args: Any?) =
    log(Log.ERROR, tag, msg, args = args, logLine = logLine)

fun logError(tag: String, t: Throwable?, msg: String? = null, vararg args: Any?, logLine: LogLine? = null) =
    log(Log.ERROR, tag, msg, t, args, logLine)


/**
 * Use case:
 *                val aForInt = 123
 *                val bForString = "six"
 *                val cForBool = true
 *                logWith(
 *                    "aForInt" to aForInt,
 *                    "bForString" to bForString,
 *                    "cForBool" to cForBool
 *                )
 *
 * Output: "aForInt: 123, bForString: six, cForBool: true"
 */
fun logWith(tag: String, vararg pairs: Pair<String, Any>) = log(tag, parseToMsg(*pairs))

fun parseToMsg(vararg pairs: Pair<String, Any?>) =
    pairs.joinToString(", ") { "${it.first}: ${it.second}" }


/**
 *   Used to improve the efficiency of logging during local testing
 *   You can modify it to your own special tag, but remember not to submit to the remote branch
 */
const val TAG_MINE = "XXXTest"

fun logMine(msg: String) = run { if (isDebug) log(TAG_MINE, msg) }

fun logMineWith(vararg pairs: Pair<String, Any?>) = logMine(parseToMsg(*pairs))

fun logIfDebug(tag: String, msg: String?) = run { if (isDebug) log(tag, msg) }


/**
 * 设置日志业务视图线
 * 用于区分不同业务的日志，便于排查问题
 */
sealed class LogLine(open val rawName: String) {
    object APP_STARTUP : LogLine("APP_STARTUP") //应用启动流程
    object APP_STATUS : LogLine("APP_STATUS")  //应用状态，比如前后台切换，页面生命周期，网络状态变化等
    object LOGIN : LogLine("LOGIN")  //登录
    object VOICE_MSG_PLAY : LogLine("VOICE_MSG_PLAY")  //语音消息播放
    object VOICE_MSG_RECORD : LogLine("VOICE_MSG_RECORD")  //语音消息录制
    object VOICE_MSG_SEND : LogLine("VOICE_MSG_SEND")  //语音消息发送
    object VOICE_MSG_RECEIVE : LogLine("VOICE_MSG_RECEIVE")  //语音消息接收
    object PUSH : LogLine("PUSH") //通知推送
    object RTC_CALL : LogLine("RTC_CALL") //视频通话
    object START_RTCAll : NestedLogLine("START", RTC_CALL) //开启rtc通话流程
    object MY_RTC_CAMERA_STATE : NestedLogLine("CAMERA_STATE", RTC_CALL)  //实时通话自己的摄像头状态变更

    /**支持嵌套的LogLine*/
    open class NestedLogLine(override val rawName: String, vararg val parents: LogLine) : LogLine(rawName){
        override fun toString(): String {
            return parents.joinToString("") { "[${it.rawName}]" }.plus("{$rawName}")
        }
    }

    /**支持多个LogLine叠加*/
    class CombinedLogLine(val lines: Set<LogLine>) : LogLine("combined"){
        override fun toString(): String {
            return lines.joinToString {
                when (it) {
                    is NestedLogLine -> it.toString()
                    else -> "[${it.rawName}]"
                }
            }
        }
    }

    /**支持使用+拼接*/
    operator fun plus(other: LogLine): CombinedLogLine {
        val current = if (this is CombinedLogLine) this.lines else setOf(this)
        val additional = if (other is CombinedLogLine) other.lines else setOf(other)
        return CombinedLogLine(current union additional)
    }


    override fun toString(): String {
        return when (this) {
            is NestedLogLine -> this.toString() //格式：[parent1][parent2]...{self}
            is CombinedLogLine -> this.toString() //格式：[parent1]{self1},[parent2]{self2} or [rawName],[rawName]
            else -> "[$rawName]"
        }
    }
}
