@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.compose

import androidx.constraintlayout.compose.ConstrainScope
import androidx.constraintlayout.compose.ConstrainedLayoutReference
import androidx.constraintlayout.compose.Dimension

fun ConstrainScope.linkToRef(
    ref: ConstrainedLayoutReference,
    width: Dimension = Dimension.wrapContent,
    height: Dimension = Dimension.wrapContent,
) {
    top.linkTo(ref.top)
    bottom.linkTo(ref.bottom)
    start.linkTo(ref.start)
    end.linkTo(ref.end)
    this.width = width
    this.height = height
}

fun ConstrainScope.linkToParent(
    width: Dimension = Dimension.wrapContent,
    height: Dimension = Dimension.wrapContent,
) = linkToRef(parent, width, height)

fun ConstrainScope.fillToRef(ref: ConstrainedLayoutReference) = linkToRef(
    ref = ref,
    width = Dimension.fillToConstraints,
    height = Dimension.fillToConstraints
)

fun ConstrainScope.fillToParent() = linkToParent(
    width = Dimension.fillToConstraints,
    height = Dimension.fillToConstraints
)

fun ConstrainScope.linkStartEndToParent(){
    start.linkTo(parent.start)
    end.linkTo(parent.end)
}

fun ConstrainScope.linkTopBottomToParent(){
    top.linkTo(parent.top)
    bottom.linkTo(parent.bottom)
}