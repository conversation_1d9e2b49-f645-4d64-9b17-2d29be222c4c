@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.compose

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp

/**
 * 设置横向间隔dp
 * Spacer with horizontal
 */
@Composable
fun HorizontalSpace(dp: Dp) {
    Spacer(Modifier.width(dp))
}

@Composable
fun VerticalSpace(dp: Dp) {
    Spacer(Modifier.height(dp))
}

@Composable
fun RowScope.WeightSpace(weight: Float) {
    Spacer(Modifier.weight(weight))
}

@Composable
fun ColumnScope.WeightSpace(weight: Float) {
    Spacer(Modifier.weight(weight))
}
