@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.ktx

import android.text.Editable
import androidx.core.net.toUri
import com.yibasan.lizhifm.sdk.platformtools.Md5Util
import org.json.JSONObject
import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.regex.Pattern


fun String?.takeIfNotNullAndNotEmpty(): String? {
    return this.takeIf { !this.isNullOrEmpty() }
}

inline fun CharSequence?.ifNullOrEmpty(defaultValue: () -> CharSequence): CharSequence {
    return if (isNullOrEmpty()) defaultValue() else this
}

fun CharSequence?.notEmpty(): Boolean = this?.isNotBlank() == true

fun CharSequence?.isEmpty(): Boolean = this?.isBlank() ?: false

fun String.limitLength(length: Int): String =
    if (this.length <= length) this else substring(0, length)

fun String?.getFirstLetter(): String =
    if (this?.isNotEmpty() == true) this[0].toString() else ""

fun String?.getFirstUppercaseLetter(): String =
    if (this?.isNotEmpty() == true) this[0].uppercase() else ""



fun String.isJson(): Boolean =
    try {
        JSONObject(this)
        true
    } catch (e: Exception) {
        false
    }

fun String?.isEmptyJsonString(): Boolean = this.isNullOrEmpty() || this == "{}" || this == "[]"

fun Float.toNumberString(fractionDigits: Int = 2, minIntDigits: Int = 1, isGrouping: Boolean = false, isHalfUp: Boolean = true): String =
    toDouble().toNumberString(fractionDigits, minIntDigits, isGrouping, isHalfUp)

fun String.toEditable(): Editable =  Editable.Factory.getInstance().newEditable(this)

fun Double.toNumberString(fractionDigits: Int = 2, minIntDigits: Int = 1, isGrouping: Boolean = false, isHalfUp: Boolean = true): String =
    (NumberFormat.getInstance() as DecimalFormat).apply {
        isGroupingUsed = isGrouping
        roundingMode = if (isHalfUp) RoundingMode.HALF_UP else RoundingMode.DOWN
        minimumIntegerDigits = minIntDigits
        minimumFractionDigits = fractionDigits
        maximumFractionDigits = fractionDigits
    }.format(this)

fun String.getClassName(): String =
    try {
        substring(lastIndexOf(".") + 1, lastIndexOf("@"))
    } catch (e: Exception) {
        this
    }

fun String.containsIgnoreCase(s:String):Boolean = this.lowercase().contains(s.lowercase())
fun String.equalsIgnoreCase(s:String):Boolean = this.lowercase() == s.lowercase()


fun String.getLocalPath(): String? {
    if (isEmpty()) {
        return null
    }
    // Check input path to replace unnecessary parts
    return when {
        contains("file:///") -> replace("file:///", "")
        contains("file://") -> replace("file://", "")
        contains("file:/") -> replace("file:/", "")
        else -> this
    }
}

fun String.isContainsContinuousChar(c:Char):Boolean{
    for (i in 1 until  length){
        if (this[i] == c && this[i-1] == c){
            return true
        }
    }
    return false
}

fun String.containsNotSupportedChar(support: String):Boolean{
    for (c in this){
        if (!support.contains(c)){
            return true
        }
    }
    return false
}

fun String.isUrl() = matches("^https?://.*".toRegex(RegexOption.IGNORE_CASE))

fun String.isAnimation() = endsWith(".json") || endsWith(".pag")

fun String.isImage() = endsWith(".png") || endsWith(".jpg") || endsWith(".jpeg")
        || endsWith(".gif") || endsWith(".webp") || endsWith(".bmp")
        || endsWith(".svg") || endsWith(".avif")

fun String.isWebpUrl(): Boolean {
    // Check if the URL ends with ".webp" or contains ".webp" before query parameters
    val regex = Regex("""\.webp($|\?)""")
    return regex.containsMatchIn(this)
}
/**
 * 针对CDN的图片地址添加_${width}x${height}
 */
fun String.imageUrlWithSize(width: Int, height: Int): String {
    if (isUrl() && width > 0 && height > 0) {
        val lastPointIndex = lastIndexOf('.')
        val afx = if (lastPointIndex > 0) substring(lastPointIndex) else ""
        val lastUnderscoreIndex = lastIndexOf('_')
        if (lastUnderscoreIndex >= 0) {
            if (substring(lastUnderscoreIndex).matches(Regex("_\\d+x\\d+${afx}$"))) {
                return substring(0, lastUnderscoreIndex) + "_" + width + "x" + height + afx
            }
        }
        return substring(0, lastPointIndex) + "_" + width + "x" + height + afx
    }
    return this
}

fun String.isUriString():Boolean{
    return try {
        val stringUri = this.toUri()
        stringUri.scheme.isNotNull() && stringUri.authority.isNotNull()&& stringUri.path.isNotNull()
    }catch (e:Exception){
        false
    }
}

fun String.md5(): String {
    return Md5Util.getMD5String(this)
}

fun String.cropLength(maxLength: Int): String {
    val result = this.trim()
    var length = 0
    var index = 0
    result.forEach { chr ->
        length += if ((chr.code) > 127) {
            2
        } else {
            1
        }
        if (length > maxLength) {
            return result.substring(0, index) + "..."
        }
        index++
    }
    return result
}

// 判断文案是否包含中文
fun String.hasChinese(): Boolean {
    val p = Pattern.compile("[\u4e00-\u9fa5]")
    val m = p.matcher(this)
    return m.find()
}