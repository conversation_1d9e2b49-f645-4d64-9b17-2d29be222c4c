@file:Suppress("unused")

package com.vocalbeats.scaffold.core.common.ktx

import android.content.Context
import com.vocalbeats.scaffold.core.common.utils.ScreenUtil

val Context.screenWidth: Int get() = ScreenUtil.getScreenWidth(this)

val Context.screenWidthReal: Int get() = ScreenUtil.getScreenWidthReal(this)

val Context.screenHeight: Int get() = ScreenUtil.getScreenHeight(this)

val Context.screenHeightReal: Int get() = ScreenUtil.getScreenHeightReal(this)

val Context.statusBarHeight: Int get() = ScreenUtil.getStatusBarHeight(this)

val Context.navigationBarHeight: Int get() = ScreenUtil.getNavigationHeight(this)

val Context.screenHeightWithoutTopBottom: Int get() = screenHeightReal - statusBarHeight - navigationBarHeight

val Context.deviceHeightWithoutTopBottom: Int get() = deviceHeight - statusBarHeight - navigationBarHeight

val Context.deviceWidth: Int get() = ScreenUtil.getDeviceWidth(this)

val Context.deviceHeight: Int get() = ScreenUtil.getDeviceHeight(this)