plugins {
    alias(libs.plugins.vocalbeats.android.library)
    alias(libs.plugins.vocalbeats.android.library.compose)
    alias(libs.plugins.vocalbeats.hilt)
    alias(bizLibs.plugins.litchi.pods)
}

android {
    namespace = "${rootProject.ext["packageName"]}.core.common"
}

// 解决 litchi.pods 插件与 KSP 的任务依赖问题
afterEvaluate {
    tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        dependsOn(tasks.matching { it.name.startsWith("create") && it.name.endsWith("JavaTask") })
    }

    // 特别处理 KSP 任务
    tasks.matching { it.name.contains("ksp") && it.name.contains("Kotlin") }.configureEach {
        dependsOn(tasks.matching { it.name.startsWith("create") && it.name.endsWith("JavaTask") })
    }
}

dependencies {
    implementation(libs.androidx.lifecycle.process)
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.analytics)
}
