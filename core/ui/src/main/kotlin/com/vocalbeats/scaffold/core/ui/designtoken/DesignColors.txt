//
//  DesignColors.kt
//
//  Created by Design Token Generator on 12.08.2025.
//  如果因为build工程内没有依赖Compose而打包失败，请手动添加Compose依赖
//
@file:Suppress("unused")
package com.vocalbeats.designtoken

import androidx.compose.ui.graphics.Color

object DesignColors {

    val lightFuncBrand = Color(0xff000000)

    val lightFuncDanger = Color(0xffff776c)

    val lightFuncSuccess = Color(0xff8cc773)

    val lightFuncAid1 = Color(0xffa2db8a)

    val lightFuncLink = Color(0xff6596ec)

    val lightFuncWarn = Color(0xfff5b17a)

    val lightFuncAid2 = Color(0xffc6c5f3)

    val lightFuncBrand60 = Color(0x99000000)

    val lightFuncBrand20 = Color(0x33000000)

    val lightFuncBrand10 = Color(0x1a000000)

    val lightFuncBrand5 = Color(0x0d000000)

    val lightFuncDanger10 = Color(0x1aff776c)

    /** owll 首页，部分模块bg */
    val lightSurf0 = Color(0xffe6e8ed)

    /** 页面 */
    val lightSurf1 = Color(0xffffffff)

    /** 用于悬浮条、页面彩色块 */
    val lightSurf2 = Color(0xfff1efef)

    /** 弹框、pop（描边、遮罩拉开层级） */
    val lightSurf3 = Color(0xffffffff)

    /** 键盘bg */
    val lightSurf4 = Color(0xffe6e8ed)

    val lightSurfToast = Color(0xff1a1a1a)

    val lightNtBk12 = Color(0x1f0d0e12)

    val lightNtBk20 = Color(0x330d0e12)

    val lightNtBk30 = Color(0x4d0d0e12)

    val lightNtBk40 = Color(0x660d0e12)

    val lightNtBk60 = Color(0x990d0e12)

    val lightNtBk80 = Color(0xcc0d0e12)

    val lightNtBk100 = Color(0xff0d0e12)

    val lightNtBk08 = Color(0x140d0e12)

    val lightNtBk05 = Color(0x0d0d0e12)

    val lightNtBk03 = Color(0x080d0e12)

    val darkFuncBrand = Color(0xffffffff)

    val darkFuncDanger = Color(0xffff776c)

    val darkFuncSuccess = Color(0xff8cc773)

    val darkFuncAid1 = Color(0xffa2db8a)

    val darkFuncLink = Color(0xff6596ec)

    val darkFuncWarn = Color(0xfff5b17a)

    val darkFuncAid2 = Color(0xffc6c5f3)

    val darkFuncBrand60 = Color(0x99ffffff)

    val darkFuncBrand20 = Color(0x33ffffff)

    val darkFuncBrand10 = Color(0x1affffff)

    val darkFuncBrand5 = Color(0x1affffff)

    val darkFuncDanger10 = Color(0x1aff776c)

    /** owll 首页，部分模块bg */
    val darkSurf0 = Color(0xff000000)

    /** 页面 */
    val darkSurf1 = Color(0xff101012)

    /** 用于悬浮条、页面彩色块 */
    val darkSurf2 = Color(0xff1a1a1a)

    /** 弹框、pop（描边、遮罩拉开层级） */
    val darkSurf3 = Color(0xff1a1a1a)

    /** 键盘bg */
    val darkSurf4 = Color(0xff2b2b2b)

    val darkSurfToast = Color(0xff1a1a1a)

    val darkNtBk12 = Color(0x1fffffff)

    val darkNtBk20 = Color(0x33ffffff)

    val darkNtBk30 = Color(0x4dffffff)

    val darkNtBk40 = Color(0x66ffffff)

    val darkNtBk60 = Color(0x99ffffff)

    val darkNtBk80 = Color(0xccffffff)

    val darkNtBk100 = Color(0xffffffff)

    val darkNtBk08 = Color(0x14ffffff)

    val darkNtBk05 = Color(0x14ffffff)

    val darkNtBk03 = Color(0x14ffffff)

    val colorChief5 = Color(0x0d000000)

    val colorChief10 = Color(0x1a000000)

    val colorChief20 = Color(0x33000000)

    val colorChief60 = Color(0x99000000)

    val colorChief100 = Color(0xff000000)

    val colorChief100d = Color(0xffffffff)

    val colorChief60d = Color(0x99ffffff)

    val colorChief20d = Color(0x33ffffff)

    val colorChief10d = Color(0x1affffff)

    val colorChief5d = Color(0x0dffffff)

    val colorRed10 = Color(0x1aff776c)

    val colorRed20 = Color(0x33ff776c)

    val colorRed60 = Color(0x99ff776c)

    val colorRed100 = Color(0xffff776c)

    val colorAssist0 = Color(0xff8cc773)

    val colorAssist1 = Color(0xfff5b17a)

    val colorAssist4 = Color(0xffa2db8a)

    val colorAssist3 = Color(0xff6596ec)

    val colorAssist2 = Color(0xffc6c5f3)

    val black12 = Color(0x1f0d0e12)

    val black20 = Color(0x330d0e12)

    val black30 = Color(0x4d0d0e12)

    val black40 = Color(0x660d0e12)

    val black60 = Color(0x990d0e12)

    val black80 = Color(0xcc0d0e12)

    val black100 = Color(0xff0d0e12)

    val black08 = Color(0x140d0e12)

    val black05 = Color(0x0d0d0e12)

    val black03 = Color(0x080d0e12)

    val white12 = Color(0x1fffffff)

    val white20 = Color(0x33ffffff)

    val white30 = Color(0x4dffffff)

    val white40 = Color(0x66ffffff)

    val white60 = Color(0x99ffffff)

    val white80 = Color(0xccffffff)

    val white100 = Color(0xffffffff)

    val white08 = Color(0x14ffffff)

    val white05 = Color(0x0dffffff)

    val white03 = Color(0x08ffffff)

    val bg0 = Color(0xffe6e8ed)

    val bg1 = Color(0xffffffff)

    val bg2 = Color(0xfff1efef)

    /** 键盘 */
    val bg4 = Color(0xffe6e8ed)

    val bg30 = Color(0xffaeaeae)

    val bg50 = Color(0xff555555)

    val bg100 = Color(0xff313131)

    /** 键盘 */
    val bg4d = Color(0xff2b2b2b)

    val bgToast = Color(0xff1a1a1a)

    val bg1d = Color(0xff101012)

    val bg0d = Color(0xff000000)

    val bg2d = Color(0xff1a1a1a)
}
