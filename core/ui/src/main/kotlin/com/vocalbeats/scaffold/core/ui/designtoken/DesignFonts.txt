//
//  DesignFonts.kt
//
//  Created by Design Token Generator on 12.08.2025.
//  如果因为build工程内没有依赖Compose而打包失败，或者依赖后使用方法报报错java.lang.NoSuchMethodError
//  请手动添加Compose插件（kotlin2.0以上是org.jetbrains.kotlin.plugin.compose，以下是kotlinCompilerExtensionVersion）
//  以及添加Compose 组件依赖（androidx.compose.ui 和 androidx.compose.runtime）
//
@file:Suppress("unused")
package com.vocalbeats.designtoken

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.em
import androidx.compose.ui.unit.sp
import com.vocalbeats.designtoken.R

object DesignFonts {

    internal val fontFamilyBold = FontFamily(Font(resId = R.font.roboto_bold))
    internal val fontFamilyMedium = FontFamily(Font(resId = R.font.roboto_medium))
    internal val fontFamilyRegular = FontFamily(Font(resId = R.font.roboto_regular))

    /** Footnote */
    @Composable
    fun pjsRegular10() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 10.sp,
        letterSpacing = 0.em,
        lineHeight = 1.2.em
    )

    /** Footnote */
    @Composable
    fun pjsRegular12() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 12.sp,
        letterSpacing = 0.em,
        lineHeight = 1.2.em
    )

    /** Footnote、describe */
    @Composable
    fun pjsRegular14() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 14.sp,
        letterSpacing = 0.em,
        lineHeight = 1.2.em
    )

    /** Tittle、描述、输入框、列表常规字体 */
    @Composable
    fun pjsRegular16() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 16.sp,
        letterSpacing = 0.em
    )


    @Composable
    fun pjsRegular18() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 18.sp,
        letterSpacing = 0.em
    )


    @Composable
    fun pjsRegular22() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 22.sp,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsRegular24() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 24.sp,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsRegular32() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 32.sp,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsRegular48() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 48.sp,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsMedium10() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 10.sp,
        fontWeight = FontWeight.SemiBold,
        letterSpacing = 0.em,
        lineHeight = 1.2.em
    )

    /** Footnote */
    @Composable
    fun pjsMedium12() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 12.sp,
        fontWeight = FontWeight.SemiBold,
        letterSpacing = 0.em,
        lineHeight = 1.2.em
    )

    /** Footnote、Main describe */
    @Composable
    fun pjsMedium14() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 14.sp,
        fontWeight = FontWeight.SemiBold,
        letterSpacing = 0.em,
        lineHeight = 1.2.em
    )

    /** Tiele、Button列表加粗强调字体等 */
    @Composable
    fun pjsMedium16() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 16.sp,
        fontWeight = FontWeight.SemiBold,
        letterSpacing = 0.em
    )

    /** Title、弹窗标题 */
    @Composable
    fun pjsMedium18() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 18.sp,
        fontWeight = FontWeight.SemiBold,
        letterSpacing = 0.em
    )

    /** LargeTitle、Number */
    @Composable
    fun pjsMedium22() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 22.sp,
        fontWeight = FontWeight.SemiBold,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsMedium24() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 24.sp,
        fontWeight = FontWeight.SemiBold,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsMedium32() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 32.sp,
        fontWeight = FontWeight.SemiBold,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsMedium48() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 48.sp,
        fontWeight = FontWeight.SemiBold,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsBold10() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 10.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em,
        lineHeight = 1.2.em
    )


    @Composable
    fun pjsBold12() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 12.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em,
        lineHeight = 1.2.em
    )

    /** Footnote */
    @Composable
    fun pjsBold14() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 14.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em,
        lineHeight = 1.2.em
    )

    /** Title */
    @Composable
    fun pjsBold16() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 16.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em
    )

    /** Largetitle */
    @Composable
    fun pjsBold18() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 18.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em
    )


    @Composable
    fun pjsBold20() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 20.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em
    )


    @Composable
    fun pjsBold22() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 22.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )

    /** 页面大标题 */
    @Composable
    fun pjsBold24() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 24.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsBold26() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 26.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsBold28() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 28.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsBold32() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 32.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )


    @Composable
    fun pjsBold48() = getDefaultTextStyle().copy(
        fontFamily = fontFamilyRegular,
        fontSize = 48.sp,
        fontWeight = FontWeight.ExtraBold,
        letterSpacing = 0.em,
        lineHeight = 1.4.em
    )
}

@Composable
internal fun getDefaultTextStyle() = TextStyle.Default.copy(
    color = Color.White,
    fontFamily = DesignFonts.fontFamilyRegular,
    fontStyle = FontStyle.Normal,
    fontSize = 14.sp,
    letterSpacing = 0.03.em,
    lineHeight = 1.5.em,
    platformStyle = PlatformTextStyle(includeFontPadding = false)
)
