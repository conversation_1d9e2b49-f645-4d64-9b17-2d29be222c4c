//
//  DesignIcons.kt
//
//  Created by Design Token Generator on 12.08.2025.
//
@file:Suppress("unused")
package com.vocalbeats.designtoken

object DesignIcons {
    const val ArrowBack = "\ue900"
    const val ArrowEnter = "\ue901"
    const val ArrowLeft = "\ue902"
    const val ArrowRight = "\ue903"
    const val Download = "\ue904"
    const val Update = "\ue905"
    const val Copy = "\ue906"
    const val Edit = "\ue907"
    const val Guide = "\ue908"
    const val Link = "\ue909"
    const val LockSurface = "\ue90a"
    const val LockLine = "\ue90b"
    const val Phone = "\ue90c"
    const val Set = "\ue90d"
    const val Share = "\ue90e"
    const val DeleteSurface = "\ue90f"
    const val Delete_line = "\ue910"
    const val Earphone = "\ue911"
    const val Micoff = "\ue912"
    const val Micon = "\ue913"
    const val RingerNo = "\ue914"
    const val Ringer = "\ue915"
    const val RingerLine = "\ue916"
    const val SpeakerOff = "\ue917"
    const val SpeakerOn = "\ue918"
    const val Flip = "\ue919"
    const val Retry = "\ue91a"
    const val Loading = "\ue91b"
    const val Search = "\ue91c"
    const val More = "\ue91d"
    const val More1 = "\ue91e"
    const val Voice = "\ue91f"
    const val Pause = "\ue920"
    const val Play = "\ue921"
    const val InfoSurface = "\ue922"
    const val InfoLine = "\ue923"
    const val QuestionSurface = "\ue924"
    const val QuestionLine = "\ue925"
    const val WarningSurface = "\ue926"
    const val WarningLine = "\ue927"
    const val NoSurface = "\ue928"
    const val NoLine = "\ue929"
    const val No = "\ue92a"
    const val YesSurface = "\ue92b"
    const val YesLine = "\ue92c"
    const val Yes = "\ue92d"
    const val Minus = "\ue92e"
    const val Plus = "\ue92f"
    const val LockopenLine = "\ue930"
    const val FileCall = "\ue931"
    const val PictureSurface = "\ue932"
    const val Picture = "\ue933"
    const val User = "\ue934"
    const val MicLine = "\ue935"
    const val Shutdown = "\ue936"
    const val Userlist = "\ue937"
    const val Usernot = "\ue938"
    const val Userplus = "\ue939"
    const val Users = "\ue93a"
    const val Call = "\ue93b"
    const val Callnot = "\ue93c"
    const val Eyes = "\ue93d"
    const val Eyesclose = "\ue93e"
    const val Menu = "\ue93f"
    const val TimeLine = "\ue940"
    const val TimeSurface = "\ue941"
    const val Vediocut = "\ue942"
    const val Vediostyle = "\ue943"
    const val File = "\ue944"
    const val Userprofile = "\ue945"
    const val ArrowDownleft = "\ue946"
    const val ArrowUpright = "\ue947"
    const val VIP = "\ue948"
    const val Configure = "\ue949"
    const val Uncheck = "\ue94a"
    const val CallIncoming = "\ue94b"
    const val CallOutgoing = "\ue94c"
    const val IcloudDone = "\ue94d"
    const val Icloud = "\ue94e"
    const val TagLine = "\ue94f"
    const val Share2 = "\ue950"
    const val Import = "\ue951"
    const val Change = "\ue952"
    const val Home = "\ue953"
    const val Word = "\ue954"
    const val Pdf = "\ue955"
    const val PlusLine = "\ue956"
    const val PlusSurface = "\ue957"
    const val IcloudDownloadyes = "\ue958"
    const val IcloudDownload = "\ue959"
    const val IntegralLine = "\ue95a"
    const val ToText = "\ue95b"
    const val Translate = "\ue95c"
    const val ArrowBack1 = "\ue95d"
    const val OnlinePoint = "\ue95e"
    const val Chathistory = "\ue95f"
    const val ThumbsdownLine = "\ue960"
    const val ThumbsdownSurface = "\ue961"
    const val Import1 = "\ue962"
    const val Down = "\ue963"
    const val ThumbsupLine = "\ue964"
    const val ThumbsupSurface = "\ue965"
    const val Pro = "\ue966"
    const val Back = "\ue967"
    const val Forward = "\ue968"
    const val FileMemo = "\ue969"
    const val All = "\ue96a"
    const val Lightning1 = "\ue96b"
    const val Lightning = "\ue96c"
    const val Medal = "\ue96d"
    const val Overlay = "\ue96e"
    const val Send = "\ue96f"
    const val Integral = "\ue970"
    const val Qrcode = "\ue971"
    const val Scan = "\ue972"
    const val Check = "\ue973"
    const val CallIncoming1 = "\ue974"
    const val Star = "\ue975"
    const val Keyboard = "\ue976"
    const val Calendar = "\ue977"
    const val CheckboxSel = "\ue978"
    const val Checkbox = "\ue979"
    const val LanguageSel = "\ue97a"
    const val Language = "\ue97b"
    const val List = "\ue97c"
    const val Sun = "\ue97d"
    const val Box = "\ue97e"
    const val Leave = "\ue97f"
    const val Water = "\ue980"
    const val Fire = "\ue981"
    const val Change1 = "\ue982"
    const val HeartSurface = "\ue983"
    const val Heart = "\ue984"
    const val No2 = "\ue985"
    const val Stop = "\ue986"
    const val Timer = "\ue987"
    const val Ringer1 = "\ue988"
    const val SkipLeft = "\ue989"
    const val SkipRight = "\ue98a"
    const val SetSurface = "\ue98b"
    const val Ringtone = "\ue98c"
    const val BrushSurface = "\ue98d"
    const val Brush = "\ue98e"
    const val Book = "\ue98f"
    const val Ppt = "\ue990"
    const val Url = "\ue991"
    const val Youtube = "\ue992"
    const val FeedbackSurface = "\ue993"
    const val Camera = "\ue994"
    const val Feedback = "\ue995"
    const val UploadSur = "\ue996"
    const val ArrowUp = "\ue997"
    const val DifficultySurface = "\ue998"
    const val RecipeSurface = "\ue999"
    const val Print = "\ue99a"
    const val RobotoSurface = "\ue99b"
    const val Roboto = "\ue99c"
    const val PrintSurface = "\ue99d"
    const val Textsel = "\ue99e"
    const val CalendarFill = "\ue99f"
    const val Location = "\ue9a0"
    const val Retake = "\ue9a1"
    const val Scanplant = "\ue9a2"
    const val Treatment = "\ue9a3"
    const val Repeat = "\ue9a4"
    const val CantCheck = "\ue9a5"
    const val Fertilizer = "\ue9a6"
    const val Bible = "\ue9a7"
    const val TagSurface = "\ue9a8"
    const val Tag = "\ue9a9"
    const val Education = "\ue9aa"
    const val Financial = "\ue9ab"
    const val Healthcare = "\ue9ac"
    const val Industry = "\ue9ad"
    const val Law = "\ue9ae"
    const val Technology = "\ue9af"
    const val Travel = "\ue9b0"
}
