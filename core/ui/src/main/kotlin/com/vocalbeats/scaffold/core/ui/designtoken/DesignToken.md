## 设计令牌本地映射说明

由于 `com.vocalbeats.designtoken` 是远端依赖，本地无法直接搜索到其内容，为了支持 Figma MCP 生成 Compose UI 时能够正确映射设计元素，我们在本地创建了对应的 txt 文件作为映射参考。

### 本地映射文件

- `DesignColors.txt` - 包含所有可用的颜色定义
- `DesignFonts.txt` - 包含所有可用的字体样式定义
- `DesignIcons.txt` - 包含所有可用的图标定义

### 使用规则

1. **颜色使用**：查找 `DesignColors.txt` 中定义的颜色，在代码中使用 `com.vocalbeats.designtoken.DesignColors.xxx`
2. **字体使用**：查找 `DesignFonts.txt` 中定义的字体样式，在代码中使用 `com.vocalbeats.designtoken.DesignFonts.xxx()`
3. **图标使用**：查找 `DesignIcons.txt` 中定义的图标，在代码中使用 `com.vocalbeats.designtoken.DesignIcons.xxx`，并配合 `IconFontText` 组件显示

### 注意事项

- txt 文件仅用于本地开发时的元素查找和映射
- 实际代码中必须使用远端依赖的完整包名 `com.vocalbeats.designtoken`
- 定期同步远端依赖内容到本地 txt 文件，确保映射准确性