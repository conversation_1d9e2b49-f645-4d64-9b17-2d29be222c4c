package com.vocalbeats.scaffold.feature.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2025/8/1
 * @desc
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    // TODO: Add repository
) : ViewModel() {

    val aFlow = MutableSharedFlow<Boolean>()

    init {
        viewModelScope.launch {
            aFlow.collect {
                delay(1000)
            }
        }
    }

}