package com.vocalbeats.scaffold.feature.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.vocalbeats.scaffold.core.common.R
import com.vocalbeats.scaffold.core.common.ktx.toast
import com.vocalbeats.scaffold.core.network.Resp
import com.vocalbeats.scaffold.data.repository.FeedbackRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class FeedbackViewModel @Inject constructor(
    private val feedbackRepository: FeedbackRepository
) : ViewModel() {
    private val _eventToUI = MutableSharedFlow<EventToUI>()
    val eventToUI: SharedFlow<EventToUI> = _eventToUI.asSharedFlow()

    fun feedback(description: String, email: String?){
        viewModelScope.launch {
            val resp = feedbackRepository.feedback(description, email)
            if (resp is Resp.Success) {
                _eventToUI.emit(EventToUI.Finish)
                //TODO yangmulin更新文案
                toast(R.string.feedback_fail)
            } else {
                toast(R.string.feedback_fail)
            }
        }
    }

    sealed interface EventToUI{
        data object Finish: EventToUI
    }

}