package com.vocalbeats.scaffold.feature.settings.navigation

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.vocalbeats.scaffold.feature.settings.AboutScreen
import com.vocalbeats.scaffold.feature.settings.FeedbackScreen
import com.vocalbeats.scaffold.feature.settings.SettingsScreen
import kotlinx.serialization.Serializable

/**
 * <AUTHOR>
 * @date 2025/8/11
 * @desc
 */
@Serializable
data object SettingRoute
@Serializable
data object AboutRoute

@Serializable
data object FeedbackRoute

fun NavController.navToSettings() = navigate(SettingRoute)

fun NavController.navToAbout() = navigate(AboutRoute)

fun NavController.navToFeedback() = navigate(FeedbackRoute)

fun NavGraphBuilder.settingsScreen(
    onNavBack: () -> Unit,
    onNavToSubscribe: () -> Unit,
    onNavToAbout: () -> Unit,
    onNavToFeedback: () -> Unit,
) {
    composable<SettingRoute> {
        SettingsScreen(
            onNavToSubscribe = onNavToSubscribe,
            onNavBack = onNavBack,
            onNavToAbout = onNavToAbout,
            onNavToFeedback = onNavToFeedback,
        )
    }
}

fun NavGraphBuilder.aboutScreen(
    onNavBack: () -> Unit,
    onNavToPrivacyPolicy: () -> Unit = {},
    onNavToTermsOfUse: () -> Unit = {},
) {
    composable<AboutRoute> {
        AboutScreen(
            onNavBack = onNavBack,
            onNavToPrivacyPolicy = onNavToPrivacyPolicy,
            onNavToTermsOfUse = onNavToTermsOfUse,
        )
    }
}

fun NavGraphBuilder.feedbackScreen(
    onNavBack: () -> Unit,
) {
    composable<FeedbackRoute> {
        FeedbackScreen(
            onNavBack = onNavBack,
        )
    }
}