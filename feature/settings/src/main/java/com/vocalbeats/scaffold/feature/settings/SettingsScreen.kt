package com.vocalbeats.scaffold.feature.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.vocalbeats.designtoken.DesignColors
import com.vocalbeats.designtoken.DesignFonts
import com.vocalbeats.designtoken.DesignIcons
import com.vocalbeats.scaffold.core.common.R
import com.vocalbeats.scaffold.core.common.compose.asString
import com.vocalbeats.scaffold.core.common.compose.debouncedClickable
import com.vocalbeats.scaffold.core.ui.component.CommonTitleBar
import com.vocalbeats.scaffold.core.ui.component.IconFontText

/**
 * <AUTHOR>
 * @date 2025/8/1
 * @desc Settings Screen - 设置页面
 */
@Composable
fun SettingsScreen(
    onNavBack: () -> Unit = {},
    onNavToSubscribe: () -> Unit,
    onNavToAbout: () -> Unit = {},
    onNavToFeedback: () -> Unit = {},
    viewModel: SettingsViewModel = hiltViewModel(),
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(DesignColors.white100)
            .statusBarsPadding()
    ) {
        // 顶部导航栏
        CommonTitleBar(
            titleRes = R.string.settings_title,
            onNavBack = onNavBack
        )

        // 内容区域
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp)
                .padding(top = 20.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // 订阅卡片
            SubscribeCard(
                onNavToSubscribe = onNavToSubscribe,
                modifier = Modifier.fillMaxWidth()
            )

            // 设置选项列表
            SettingsOptionsCard(
                onNavToAbout = onNavToAbout,
                onNavToFeedback = onNavToFeedback,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 订阅卡片组件
 */
@Composable
private fun SubscribeCard(
    onNavToSubscribe: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .background(DesignColors.black100)
            .height(80.dp)
            .debouncedClickable(onClick = onNavToSubscribe)
    ) {
        // Plus 标签
        Box(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .background(
                    color = DesignColors.lightFuncSuccess,
                    shape = RoundedCornerShape(
                        topEnd = 3.dp,
                        bottomStart = 12.dp
                    )
                )
                .padding(horizontal = 10.dp, vertical = 4.dp)
        ) {
            Text(
                text = R.string.settings_plus.asString(),
                style = DesignFonts.pjsBold12(),
                color = DesignColors.black100
            )
        }

        // 内容区域
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            IconFontText(
                icon = DesignIcons.VIP,
                size = 22.dp,
                color = DesignColors.white100
            )

            Spacer(modifier = Modifier.width(16.dp))

            // 文字区域
            Column {
                Text(
                    text = R.string.settings_my_privileges.asString(),
                    style = DesignFonts.pjsMedium16(),
                    color = DesignColors.white100
                )
                Text(
                    text = R.string.settings_unlimited_access.asString(),
                    style = DesignFonts.pjsRegular12(),
                    color = DesignColors.white80
                )
            }
        }
    }
}

/**
 * 设置选项卡片组件
 */
@Composable
private fun SettingsOptionsCard(
    onNavToAbout: () -> Unit,
    onNavToFeedback: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .background(DesignColors.lightNtBk05)
    ) {
        // About 选项
        SettingsOptionItem(
            icon = DesignIcons.Phone,
            title = R.string.settings_about.asString(),
            onClick = onNavToAbout
        )

        // Feedback 选项
        SettingsOptionItem(
            icon = DesignIcons.QuestionLine,
            title = R.string.settings_feedback.asString(),
            onClick = onNavToFeedback
        )
    }
}

/**
 * 设置选项项组件
 */
@Composable
private fun SettingsOptionItem(
    icon: String,
    title: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(64.dp)
            .debouncedClickable(onClick = onClick)
            .padding(horizontal = 20.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧图标
        IconFontText(
            icon = icon,
            size = 22.dp,
            color = DesignColors.black100
        )

        Spacer(modifier = Modifier.width(16.dp))

        // 标题
        Text(
            text = title,
            style = DesignFonts.pjsMedium16(),
            color = DesignColors.black100,
            modifier = Modifier.weight(1f)
        )

        // 右侧箭头
        IconFontText(
            icon = DesignIcons.ArrowEnter,
            size = 16.dp,
            color = DesignColors.black40
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun SubscribeCardPreview() {
    SubscribeCard({})
}

@Preview(showBackground = true)
@Composable
private fun SettingsOptionsCardPreview() {
    SettingsOptionsCard(
        onNavToAbout = {},
        onNavToFeedback = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun SettingsScreenPreview() {
    SettingsScreen(
        onNavBack = {},
        onNavToFeedback = {},
        onNavToSubscribe = {}
    )
}