package com.vocalbeats.scaffold.feature.onboarding.navigation

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.vocalbeats.scaffold.feature.onboarding.OnboardingScreen
import com.vocalbeats.scaffold.feature.onboarding.R
import kotlinx.serialization.Serializable
import com.vocalbeats.scaffold.core.common.R as CommonR

@Serializable
data object OnboardingRoute

fun NavController.navToOnboarding() = navigate(OnboardingRoute)

fun NavGraphBuilder.onboardingScreen(
    onPrivacyPolicyClick: () -> Unit = {},
    onTermsOfServiceClick: () -> Unit = {},
    onRestoreClick: () -> Unit = {},
    onContinueClick: () -> Unit,
) {
    composable<OnboardingRoute> {
        OnboardingScreen(
            imageRes = R.drawable.onboarding_illustration,
            titleRes = CommonR.string.guidance_title,
            tipRes = CommonR.string.guidance_tips,
            onPrivacyPolicyClick = onPrivacyPolicyClick,
            onTermsOfServiceClick = onTermsOfServiceClick,
            onRestoreClick = onRestoreClick,
            onContinueClick = onContinueClick,
        )
    }
}