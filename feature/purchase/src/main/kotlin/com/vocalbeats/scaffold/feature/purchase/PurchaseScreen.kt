package com.vocalbeats.scaffold.feature.purchase

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel

@Composable
fun PurchaseScreen(
    modifier: Modifier = Modifier,
    viewModel: PurchaseViewModel = hiltViewModel(),
) {
    // TODO: implement UI
}

@Preview
@Composable
private fun PurchaseScreenPreview() {
    PurchaseScreen()
}