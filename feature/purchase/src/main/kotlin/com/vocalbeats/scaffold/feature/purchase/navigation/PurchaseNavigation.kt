package com.vocalbeats.scaffold.feature.purchase.navigation

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.vocalbeats.scaffold.feature.purchase.PurchaseScreen
import kotlinx.serialization.Serializable

/**
 * <AUTHOR>
 * @date 2025/8/20
 * @desc
 */
@Serializable
data object PurchaseRoute

fun NavController.navToPurchase() = navigate(PurchaseRoute)

fun NavGraphBuilder.purchaseScreen() {
    composable<PurchaseRoute> {
        PurchaseScreen()
    }
}