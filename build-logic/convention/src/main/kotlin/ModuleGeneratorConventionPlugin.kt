
import org.gradle.api.DefaultTask
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.tasks.TaskAction
import java.io.File

/**
 * <AUTHOR>
 * @date 2025/8/1
 * @desc 这个插件用于快速生成标准化的 Android 模块结构，包含模块目录、配置文件、源码结构，并更新 settings.gradle.kts 文件
 *       使用方法 / Usage:
 *       ./gradlew generateModule
 */
// ================================================================================================
// 模板字符串常量区域 / Template String Constants
// ================================================================================================

/**
 * Core 模块的 build.gradle.kts 模板
 * Template for Core module's build.gradle.kts
 * 参数: moduleName
 */
private const val CORE_BUILD_GRADLE_TEMPLATE = """
plugins {
    alias(libs.plugins.vocalbeats.android.library)
}

android {
    namespace = "${'$'}{rootProject.ext["packageName"]}.core.%s"
}

dependencies {
    //TODO add implementation
}
"""

/**
 * Feature 模块的 build.gradle.kts 模板
 * Template for Feature module's build.gradle.kts
 * 参数: moduleName
 */
private const val FEATURE_BUILD_GRADLE_TEMPLATE = """
plugins {
    alias(libs.plugins.vocalbeats.android.feature)
    alias(libs.plugins.vocalbeats.android.library.compose)
}

android {
    namespace = "${'$'}{rootProject.ext["packageName"]}.feature.%s"
}

dependencies {
    //TODO add implementation
}
"""

/**
 * 其它模块的 build.gradle.kts 模板
 * Template for Other module's build.gradle.kts
 * 参数: folderName, moduleName
 */
private const val OTHER_BUILD_GRADLE_TEMPLATE = """
plugins {
    alias(libs.plugins.vocalbeats.android.library)
}

android {
    namespace = "${'$'}{rootProject.ext["packageName"]}.%s.%s"
}

dependencies {
    //TODO add implementation
}
"""

private const val STANDARD_BUILD_GRADLE_TEMPLATE = """
plugins {
    alias(libs.plugins.vocalbeats.android.library)
}

android {
    namespace = "${'$'}{rootProject.ext["packageName"]}.%s"
}

dependencies {
    //TODO add implementation
}
"""

/**
 * .gitignore 文件模板
 * Template for .gitignore file
 */
private const val GITIGNORE_TEMPLATE = "/build"

/**
 * Compose Screen 模板
 * Template for Compose Screen
 * 参数: fullPackageName, capitalizedModuleName, capitalizedModuleName, capitalizedModuleName, capitalizedModuleName
 */
private const val COMPOSE_SCREEN_TEMPLATE = """package %s

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel

@Composable
fun %sScreen(
    modifier: Modifier = Modifier,
    viewModel: %sViewModel = hiltViewModel(),
) {
    // TODO: implement UI
}

@Preview
@Composable
private fun %sScreenPreview() {
    %sScreen()
}
"""

/**
 * ViewModel 模板
 * Template for ViewModel
 * 参数: fullPackageName, capitalizedModuleName, capitalizedModuleName
 */
private const val VIEW_MODEL_TEMPLATE = """package %s

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class %sViewModel @Inject constructor(
    // TODO: Add repository
) : ViewModel() {

}
"""

class ModuleGeneratorConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        // 交互式模块生成任务
        target.tasks.register("generateModule", InteractiveModuleGeneratorTask::class.java)

        // 添加简化的直接生成任务
        target.tasks.register("generateModuleInFolder", GenerateModuleInFolderTask::class.java)
        target.tasks.register("generateStandaloneModule", GenerateStandaloneModuleTask::class.java)
        target.tasks.register("createFolder", CreateFolderTask::class.java)
    }
}



/**
 * 交互式模块生成任务
 * Interactive Module Generator Task
 *
 * 提供用户友好的交互式界面来生成模块，无需记忆复杂的命令行参数
 * Provides a user-friendly interactive interface to generate modules without memorizing complex command line arguments
 */
abstract class InteractiveModuleGeneratorTask : DefaultTask() {

    @TaskAction
    fun generateModuleInteractively() {
        println("\n" + "=".repeat(60))
        println("🚀 欢迎使用交互式模块生成器 / Interactive Module Generator")
        println("=".repeat(60))

        try {
            // 选择模块类型
            val moduleTypeInfo = selectModuleType()
            if (moduleTypeInfo == null) {
                println("❌ 操作已取消")
                return
            }

            // 处理新增文件夹的情况
            val finalModuleTypeInfo = if (moduleTypeInfo.first == "new_folder") {
                val folderName = inputFolderName()
                if (folderName == null) {
                    println("❌ 操作已取消")
                    return
                }
                // 创建文件夹
                val folderDir = File(project.rootDir, folderName)
                folderDir.mkdirs()
                println("✅ 文件夹 '$folderName' 创建成功")

                // 返回新文件夹作为模块类型
                folderName to "$folderName 文件夹"
            } else {
                moduleTypeInfo
            }

            // 输入模块名称
            val moduleName = inputModuleName(finalModuleTypeInfo.first)
            if (moduleName == null) {
                println("❌ 操作已取消")
                return
            }

            // 确认信息
            if (!confirmGeneration(finalModuleTypeInfo, moduleName)) {
                println("❌ 操作已取消")
                return
            }

            // 生成模块
            generateModule(finalModuleTypeInfo, moduleName)

        } catch (e: Exception) {
            println("❌ 生成模块时发生错误: ${e.message}")
        }
    }

    /**
     * 选择模块类型
     * 返回 Pair<模块类型, 显示名称>
     */
    private fun selectModuleType(): Pair<String, String>? {
        // 获取根目录下的所有文件夹（排除特定文件夹和独立模块）
        val excludedFolders = setOf("app", "build-logic", "build", "gradle", ".gradle", ".git", ".idea")
        val existingFolders = project.rootDir.listFiles()
            ?.filter {
                it.isDirectory &&
                !it.name.startsWith(".") &&
                it.name !in excludedFolders &&
                // 排除独立模块（包含build.gradle.kts且只有src等标准Android模块目录的目录）
                !(File(it, "build.gradle.kts").exists() &&
                  it.listFiles()?.filter { subFile -> subFile.isDirectory && !subFile.name.startsWith(".") }
                    ?.all { subDir -> subDir.name in setOf("src", "build") } == true)
            }
            ?.map { it.name }
            ?.sorted()
            ?: emptyList()

        println("\n📦 请选择要生成的模块类型（位于哪个文件夹下）:")

        // 显示现有文件夹选项
        existingFolders.forEachIndexed { index, folderName ->
            println("${index + 1}. $folderName 文件夹")
        }

        // 显示固定选项
        val newFolderOption = existingFolders.size + 1
        val standaloneOption = existingFolders.size + 2
        val cancelOption = 0

        println("$newFolderOption. 新增文件夹")
        println("$standaloneOption. 根目录下直接创建模块")
        println("$cancelOption. 取消")

        while (true) {
            print("\n请输入选项 (1-${standaloneOption}/0): ")
            val input = System.`in`.bufferedReader().readLine()?.trim()

            when {
                input == "0" -> return null
                input?.toIntOrNull() in 1..existingFolders.size -> {
                    val folderIndex = input!!.toInt() - 1
                    val folderName = existingFolders[folderIndex]
                    return folderName to "$folderName 文件夹"
                }
                input?.toIntOrNull() == newFolderOption -> return "new_folder" to "新增文件夹"
                input?.toIntOrNull() == standaloneOption -> return "standalone" to "根目录模块"
                else -> println("⚠️ 无效选项，请输入 1-${standaloneOption} 或 0")
            }
        }
    }

    /**
     * 输入文件夹名称
     */
    private fun inputFolderName(): String? {
        println("\n📁 请输入新文件夹名称:")
        println("   - 使用小写字母和连字符 (例如: share")
        println("   - 不要包含空格或特殊字符")
        println("   - 输入 'cancel' 取消操作")

        while (true) {
            print("\n文件夹名称: ")
            val input = System.`in`.bufferedReader().readLine()?.trim()

            when {
                input.isNullOrEmpty() -> {
                    println("⚠️ 文件夹名称不能为空，请重新输入")
                    continue
                }
                input.equals("cancel", ignoreCase = true) -> return null
                !isValidModuleName(input) -> {
                    println("⚠️ 文件夹名称格式不正确，请使用小写字母、数字和连字符")
                    continue
                }
                File(project.rootDir, input).exists() -> {
                    println("⚠️ 文件夹已存在，请选择其他名称")
                    continue
                }
                else -> return input
            }
        }
    }

    /**
     * 输入模块名称
     */
    private fun inputModuleName(moduleType: String): String? {
        println("\n📝 请输入模块名称:")
        println("   - 使用小写字母和连字符 (例如: setting)")
        println("   - 不要包含空格或特殊字符")
        println("   - 输入 'cancel' 取消操作")

        while (true) {
            print("\n模块名称: ")
            val input = System.`in`.bufferedReader().readLine()?.trim()

            when {
                input.isNullOrEmpty() -> {
                    println("⚠️ 模块名称不能为空，请重新输入")
                    continue
                }
                input.equals("cancel", ignoreCase = true) -> return null
                !isValidModuleName(input) -> {
                    println("⚠️ 模块名称格式不正确，请使用小写字母、数字和连字符")
                    continue
                }
                moduleExists(input, moduleType) -> {
                    println("⚠️ 模块已存在，请选择其他名称")
                    continue
                }
                else -> return input
            }
        }
    }

    /**
     * 验证模块名称格式
     */
    private fun isValidModuleName(name: String): Boolean {
        return name.matches(Regex("^[a-z][a-z0-9-]*[a-z0-9]$")) || name.matches(Regex("^[a-z]$"))
    }

    /**
     * 检查模块是否已存在
     */
    private fun moduleExists(moduleName: String, moduleType: String): Boolean {
        return when (moduleType) {
            "core" -> File(project.rootDir, "core/$moduleName").exists()
            "feature" -> File(project.rootDir, "feature/$moduleName").exists()
            "standalone" -> File(project.rootDir, moduleName).exists()
            else -> false
        }
    }



    /**
     * 确认生成信息
     */
    private fun confirmGeneration(moduleTypeInfo: Pair<String, String>, moduleName: String): Boolean {
        val (moduleType, typeDisplay) = moduleTypeInfo
        val modulePath = when (moduleType) {
            "standalone" -> moduleName
            else -> "$moduleType/$moduleName"
        }

        println("\n" + "-".repeat(40))
        println("📋 生成信息确认:")
        println("   模块类型: $typeDisplay")
        println("   模块名称: $moduleName")
        println("   生成路径: $modulePath")
        println("   确认生成? (y/n): ")
        println("-".repeat(40))

        while (true) {
            val input = System.`in`.bufferedReader().readLine()?.trim()?.lowercase()

            when (input) {
                "y", "yes", "是" -> return true
                "n", "no", "否" -> return false
                else -> println("⚠️ 请输入 y 或 n")
            }
        }
    }

    /**
     * 生成模块
     */
    private fun generateModule(moduleTypeInfo: Pair<String, String>, moduleName: String) {
        val (moduleType, typeDisplay) = moduleTypeInfo
        println("\n🔨 正在生成模块...")

        // 读取 project.properties 中的包名
        val basePackageName = readPackageNameFromProperties()

        val rootDir = project.rootDir
        val moduleDir = when (moduleType) {
            "standalone" -> File(rootDir, moduleName)
            else -> File(rootDir, "$moduleType/$moduleName")
        }

        if (moduleDir.exists()) {
            println("模块 $moduleName 已存在")
            return
        }

        when (moduleType) {
            "core" -> generateCoreModule(moduleDir, basePackageName, moduleName)
            "feature" -> generateFeatureModule(moduleDir, basePackageName, moduleName)
            "standalone" -> generateStandaloneModule(moduleDir, basePackageName, moduleName)
            else -> generateGenericModule(moduleDir, basePackageName, moduleName, moduleType)
        }

        updateSettingsGradle(moduleType, moduleName)

        println("\n" + "=".repeat(60))
        println("✅ 模块生成完成!")
        val modulePath = when (moduleType) {
            "standalone" -> moduleName
            else -> "$moduleType/$moduleName"
        }
        println("📁 模块路径: $modulePath")
        println("🔄 settings.gradle.kts 已自动更新")

        if (moduleType == "feature") {
            println("📱 已生成 Compose Screen 和 ViewModel 模板")
        }

        println("\n💡 下一步:")
        println("   1. 刷新项目 (Sync Project)")
        println("   2. 在 $modulePath/build.gradle.kts 中添加依赖")
        if (moduleType == "feature") {
            val className = moduleName.split("-").joinToString("") {
                it.replaceFirstChar { char -> if (char.isLowerCase()) char.titlecase() else char.toString() }
            }
            println("   3. 实现 ${className}Screen 的UI逻辑")
        }
        println("=".repeat(60))
    }

    /**
     * 从 project.properties 文件中读取包名
     */
    fun readPackageNameFromProperties(): String {
        val propertiesFile = File(project.rootDir, "project.properties")
        if (!propertiesFile.exists()) {
            println("⚠️ project.properties 文件不存在，使用默认包名")
            return "com.vocalbeats.scaffold"
        }

        val properties = java.util.Properties()
        propertiesFile.inputStream().use { properties.load(it) }

        return properties.getProperty("packageName", "com.vocalbeats.scaffold").also {
            println("📦 使用包名: $it")
        }
    }

    /**
     * 生成Core模块
     */
    private fun generateCoreModule(moduleDir: File, basePackageName: String, moduleName: String) {
        moduleDir.mkdirs()

        // 将连字符转换为下划线用于包名
        val packageModuleName = moduleName.replace("-", "_")

        // 创建 build.gradle.kts
        createBuildGradle(moduleDir, CORE_BUILD_GRADLE_TEMPLATE.format(packageModuleName).trimIndent())

        // 创建 .gitignore
        createGitignore(moduleDir)

        // 创建源码目录结构
        val fullPackageName = "$basePackageName.core.$packageModuleName"
        createSourceStructure(moduleDir, fullPackageName)
    }

    /**
     * 生成Feature模块
     */
    private fun generateFeatureModule(moduleDir: File, basePackageName: String, moduleName: String) {
        moduleDir.mkdirs()

        // 将连字符转换为下划线用于包名
        val packageModuleName = moduleName.replace("-", "_")

        // 创建 build.gradle.kts
        createBuildGradle(moduleDir, FEATURE_BUILD_GRADLE_TEMPLATE.format(packageModuleName).trimIndent())

        // 创建 .gitignore
        createGitignore(moduleDir)

        // 创建源码目录结构和 Compose 文件
        val fullPackageName = "$basePackageName.feature.$packageModuleName"
        createSourceStructure(moduleDir, fullPackageName)
        createFeatureTemplateFiles(moduleDir, fullPackageName, moduleName)
    }

    /**
     * 生成独立模块
     */
    fun generateStandaloneModule(moduleDir: File, basePackageName: String, moduleName: String) {
        moduleDir.mkdirs()

        // 将连字符转换为下划线用于包名
        val packageModuleName = moduleName.replace("-", "_")

        // 创建 build.gradle.kts
        createBuildGradle(moduleDir, STANDARD_BUILD_GRADLE_TEMPLATE.format(packageModuleName).trimIndent())

        // 创建 .gitignore
        createGitignore(moduleDir)

        // 创建源码目录结构
        val fullPackageName = "$basePackageName.$packageModuleName"
        createSourceStructure(moduleDir, fullPackageName)
    }

    /**
     * 生成通用模块（用于动态文件夹）
     */
    private fun generateGenericModule(moduleDir: File, basePackageName: String, moduleName: String, folderType: String) {
        moduleDir.mkdirs()

        // 将连字符转换为下划线用于包名
        val packageModuleName = moduleName.replace("-", "_")
        File(moduleDir, "build.gradle.kts").writeText(OTHER_BUILD_GRADLE_TEMPLATE.format(folderType, packageModuleName).trimIndent())

        // 创建 .gitignore
        createGitignore(moduleDir)

        // 创建源码目录结构
        val fullPackageName = "$basePackageName.$folderType.$packageModuleName"
        createSourceStructure(moduleDir, fullPackageName)
    }

    /**
     * 创建build.gradle.kts文件
     */
    private fun createBuildGradle(moduleDir: File, content: String) {
        File(moduleDir, "build.gradle.kts").writeText(content)
    }

    /**
     * 创建.gitignore文件
     */
    private fun createGitignore(moduleDir: File) {
        File(moduleDir, ".gitignore").writeText(GITIGNORE_TEMPLATE)
    }

    /**
     * 创建源码目录结构
     */
    private fun createSourceStructure(moduleDir: File, packageName: String) {
        val packagePath = packageName.replace(".", "/")
        val mainDir = File(moduleDir, "src/main/kotlin/$packagePath")
        mainDir.mkdirs()
    }

    /**
     * 创建Feature模块的模板文件
     */
    private fun createFeatureTemplateFiles(moduleDir: File, fullPackageName: String, moduleName: String) {
        val packagePath = fullPackageName.replace(".", "/")
        val mainDir = File(moduleDir, "src/main/kotlin/$packagePath")
        val capitalizedModuleName = moduleName.split("-").joinToString("") {
            it.replaceFirstChar { char -> if (char.isLowerCase()) char.titlecase() else char.toString() }
        }

        // 创建 Screen
        File(mainDir, "${capitalizedModuleName}Screen.kt").writeText(
            COMPOSE_SCREEN_TEMPLATE.format(
                fullPackageName,               // package name
                capitalizedModuleName,         // function name
                capitalizedModuleName,         // ViewModel type
                capitalizedModuleName,         // Preview function name
                capitalizedModuleName          // Preview function call
            ).trimIndent()
        )

        // 创建 ViewModel
        File(mainDir, "${capitalizedModuleName}ViewModel.kt").writeText(
            VIEW_MODEL_TEMPLATE.format(
                fullPackageName,               // package name
                capitalizedModuleName,         // class name
                capitalizedModuleName          // class name
            ).trimIndent()
        )
    }

    /**
     * 更新settings.gradle.kts文件
     */
    fun updateSettingsGradle(moduleType: String, moduleName: String) {
        val settingsFile = File(project.rootDir, "settings.gradle.kts")
        val content = settingsFile.readText()

        val newInclude = when (moduleType) {
            "standalone" -> "include(\":$moduleName\")"
            else -> "include(\":$moduleType:$moduleName\")"
        }

        if (!content.contains(newInclude)) {
            val lines = content.lines().toMutableList()
            val lastIncludeIndex = lines.indexOfLast { it.startsWith("include(") }

            if (lastIncludeIndex != -1) {
                lines.add(lastIncludeIndex + 1, newInclude)
                settingsFile.writeText(lines.joinToString("\n"))
            }
        }
    }
}

/**
 * 生成独立模块的简单任务
 */
abstract class GenerateStandaloneModuleTask : DefaultTask() {
    @TaskAction
    fun generateStandaloneModule() {
        val moduleName = project.findProperty("moduleName") as? String
        if (moduleName.isNullOrEmpty()) {
            println("请使用 -PmoduleName=yourModuleName 指定模块名称")
            return
        }

        // 读取包名
        val basePackageName = readPackageNameFromProperties()

        // 生成独立模块
        val moduleDir = File(project.rootDir, moduleName)
        if (moduleDir.exists()) {
            println("模块 $moduleName 已存在")
            return
        }

        generateStandaloneModuleInternal(moduleDir, basePackageName, moduleName)
        updateSettingsGradleInternal("standalone", moduleName)

        println("✅ 成功创建独立模块: $moduleName")
    }

    private fun readPackageNameFromProperties(): String {
        val propertiesFile = File(project.rootDir, "project.properties")
        if (!propertiesFile.exists()) {
            println("⚠️ project.properties 文件不存在，使用默认包名")
            return "com.vocalbeats.scaffold"
        }

        val properties = java.util.Properties()
        propertiesFile.inputStream().use { properties.load(it) }

        return properties.getProperty("packageName", "com.vocalbeats.scaffold").also {
            println("📦 使用包名: $it")
        }
    }

    private fun generateStandaloneModuleInternal(moduleDir: File, basePackageName: String, moduleName: String) {
        moduleDir.mkdirs()

        // 将连字符转换为下划线用于包名
        val packageModuleName = moduleName.replace("-", "_")

        // 创建 build.gradle.kts (独立模块模板)
        val standaloneTemplate = """
plugins {
    alias(libs.plugins.vocalbeats.android.library)
}

android {
    namespace = "${'$'}{rootProject.ext["packageName"]}.%s"
}

dependencies {
    //TODO add implementation
}
"""
        File(moduleDir, "build.gradle.kts").writeText(standaloneTemplate.format(packageModuleName).trimIndent())

        // 创建 .gitignore
        File(moduleDir, ".gitignore").writeText(GITIGNORE_TEMPLATE)

        // 创建源码目录结构
        val fullPackageName = "$basePackageName.$packageModuleName"
        val packagePath = fullPackageName.replace(".", "/")
        val mainDir = File(moduleDir, "src/main/kotlin/$packagePath")
        mainDir.mkdirs()
    }

    private fun updateSettingsGradleInternal(moduleType: String, moduleName: String) {
        val settingsFile = File(project.rootDir, "settings.gradle.kts")
        val content = settingsFile.readText()

        val newInclude = when (moduleType) {
            "standalone" -> "include(\":$moduleName\")"
            else -> "include(\":$moduleType:$moduleName\")"
        }

        if (!content.contains(newInclude)) {
            val lines = content.lines().toMutableList()
            val lastIncludeIndex = lines.indexOfLast { it.startsWith("include(") }

            if (lastIncludeIndex != -1) {
                lines.add(lastIncludeIndex + 1, newInclude)
                settingsFile.writeText(lines.joinToString("\n"))
            }
        }
    }
}

/**
 * 创建文件夹的简单任务
 */
abstract class CreateFolderTask : DefaultTask() {
    @TaskAction
    fun createFolder() {
        val folderName = project.findProperty("folderName") as? String
        if (folderName.isNullOrEmpty()) {
            println("请使用 -PfolderName=yourFolderName 指定文件夹名称")
            return
        }

        val folderDir = File(project.rootDir, folderName)
        if (folderDir.exists()) {
            println("文件夹 $folderName 已存在")
            return
        }

        folderDir.mkdirs()

        println("✅ 成功创建文件夹: $folderName")
    }
}

/**
 * 在指定文件夹中生成模块的任务
 */
abstract class GenerateModuleInFolderTask : DefaultTask() {
    @TaskAction
    fun generateModuleInFolder() {
        val folderName = project.findProperty("folderName") as? String
        val moduleName = project.findProperty("moduleName") as? String

        if (folderName.isNullOrEmpty()) {
            println("请使用 -PfolderName=yourFolderName 指定文件夹名称")
            return
        }

        if (moduleName.isNullOrEmpty()) {
            println("请使用 -PmoduleName=yourModuleName 指定模块名称")
            return
        }

        // 读取包名
        val basePackageName = readPackageNameFromProperties()

        // 检查文件夹是否存在，不存在则创建
        val folderDir = File(project.rootDir, folderName)
        if (!folderDir.exists()) {
            folderDir.mkdirs()
            println("✅ 文件夹 '$folderName' 创建成功")
        }

        // 生成模块
        val moduleDir = File(folderDir, moduleName)
        if (moduleDir.exists()) {
            println("模块 $folderName/$moduleName 已存在")
            return
        }

        // 根据文件夹类型选择生成方法
        when (folderName) {
            "core" -> generateCoreModuleInternal(moduleDir, basePackageName, moduleName)
            "feature" -> generateFeatureModuleInternal(moduleDir, basePackageName, moduleName)
            else -> generateGenericModuleInternal(moduleDir, basePackageName, moduleName, folderName)
        }

        updateSettingsGradleInternal(folderName, moduleName)

        println("✅ 成功创建模块: $folderName/$moduleName")
    }

    private fun readPackageNameFromProperties(): String {
        val propertiesFile = File(project.rootDir, "project.properties")
        if (!propertiesFile.exists()) {
            println("⚠️ project.properties 文件不存在，使用默认包名")
            return "com.vocalbeats.scaffold"
        }

        val properties = java.util.Properties()
        propertiesFile.inputStream().use { properties.load(it) }

        return properties.getProperty("packageName", "com.vocalbeats.scaffold").also {
            println("📦 使用包名: $it")
        }
    }

    private fun generateCoreModuleInternal(moduleDir: File, basePackageName: String, moduleName: String) {
        moduleDir.mkdirs()
        val packageModuleName = moduleName.replace("-", "_")
        File(moduleDir, "build.gradle.kts").writeText(CORE_BUILD_GRADLE_TEMPLATE.format(packageModuleName).trimIndent())
        File(moduleDir, ".gitignore").writeText(GITIGNORE_TEMPLATE)
        val fullPackageName = "$basePackageName.core.$packageModuleName"
        val packagePath = fullPackageName.replace(".", "/")
        val mainDir = File(moduleDir, "src/main/kotlin/$packagePath")
        mainDir.mkdirs()
    }

    private fun generateFeatureModuleInternal(moduleDir: File, basePackageName: String, moduleName: String) {
        moduleDir.mkdirs()
        val packageModuleName = moduleName.replace("-", "_")
        File(moduleDir, "build.gradle.kts").writeText(FEATURE_BUILD_GRADLE_TEMPLATE.format(packageModuleName).trimIndent())
        File(moduleDir, ".gitignore").writeText(GITIGNORE_TEMPLATE)
        val fullPackageName = "$basePackageName.feature.$packageModuleName"
        val packagePath = fullPackageName.replace(".", "/")
        val mainDir = File(moduleDir, "src/main/kotlin/$packagePath")
        mainDir.mkdirs()

        // 创建Feature模板文件
        val capitalizedModuleName = moduleName.split("-").joinToString("") {
            it.replaceFirstChar { char -> if (char.isLowerCase()) char.titlecase() else char.toString() }
        }

        File(mainDir, "${capitalizedModuleName}Screen.kt").writeText(
            COMPOSE_SCREEN_TEMPLATE.format(
                fullPackageName, capitalizedModuleName, capitalizedModuleName,
                capitalizedModuleName, capitalizedModuleName
            ).trimIndent()
        )

        File(mainDir, "${capitalizedModuleName}ViewModel.kt").writeText(
            VIEW_MODEL_TEMPLATE.format(
                fullPackageName, capitalizedModuleName, capitalizedModuleName
            ).trimIndent()
        )
    }

    private fun generateGenericModuleInternal(moduleDir: File, basePackageName: String, moduleName: String, folderType: String) {
        moduleDir.mkdirs()
        val packageModuleName = moduleName.replace("-", "_")
        File(moduleDir, "build.gradle.kts").writeText(OTHER_BUILD_GRADLE_TEMPLATE.format(folderType, packageModuleName).trimIndent())
        File(moduleDir, ".gitignore").writeText(GITIGNORE_TEMPLATE)
        val fullPackageName = "$basePackageName.$folderType.$packageModuleName"
        val packagePath = fullPackageName.replace(".", "/")
        val mainDir = File(moduleDir, "src/main/kotlin/$packagePath")
        mainDir.mkdirs()
    }

    private fun updateSettingsGradleInternal(folderName: String, moduleName: String) {
        val settingsFile = File(project.rootDir, "settings.gradle.kts")
        val content = settingsFile.readText()
        val newInclude = "include(\":$folderName:$moduleName\")"

        if (!content.contains(newInclude)) {
            val lines = content.lines().toMutableList()
            val lastIncludeIndex = lines.indexOfLast { it.startsWith("include(") }

            if (lastIncludeIndex != -1) {
                lines.add(lastIncludeIndex + 1, newInclude)
                settingsFile.writeText(lines.joinToString("\n"))
            }
        }
    }
}