import org.gradle.api.DefaultTask
import org.gradle.api.GradleException
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.tasks.TaskAction
import groovy.json.JsonSlurper
import java.io.File
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.util.regex.Pattern

/**
 * <AUTHOR> by AI Assistant
 * @desc 用于从飞书文档生成埋点代码的Convention Plugin
 *       使用方法: ./gradlew generateTrackCode -PsheetUrl="https://..."
 */

/**
 * Sheet数据类
 */
data class Sheet(
    val title: String,
    val id: String
)

/**
 * 埋点代码生成插件
 */
class TrackCodeGeneratorConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.tasks.register("generateTrackCode", GenerateTrackCodeTask::class.java)
    }
}

/**
 * 生成埋点代码的任务
 */
abstract class GenerateTrackCodeTask : DefaultTask() {

    init {
        group = "code generation"
        description = "从飞书文档生成埋点代码"
    }

    @TaskAction
    fun generateTrackCode() {
        // 从project.properties读取packageName
        val packageName = project.rootProject.extensions.extraProperties.get("packageName") as? String 
            ?: throw GradleException("无法读取packageName，请检查project.properties文件")
        
        // 构建动态文件路径
        val packagePath = packageName.replace(".", "/")
        val fileDir = "core/common/src/main/java/$packagePath/core/common/track/"
        val fileName = "AppTracker.kt"
        
        // 配置信息
        val appid = "cli_a5222bd7e378d010_"
        val appsecret = "kO7P013s41buBHj5ZAgRThsBtCEICvg3"
        
        println("使用packageName: $packageName")
        println("生成文件路径: $fileDir$fileName")

        var sheetToken: String? = null

        // 检查参数
        if (!project.hasProperty("sheetUrl")) {
            throw GradleException("请提供sheetUrl参数: ./gradlew generateTrackCode -PsheetUrl=https://vocalbeats.sg.larksuite.com/sheets/xxx")
        }

        val input = project.property("sheetUrl") as String
        val sheetFid = getSheetFid(input)
        println("获取到的sheet_fid: $sheetFid")

        if (sheetToken == null) {
            sheetToken = refreshToken(appid, appsecret)
        }

        val token = sheetToken ?: return
        val sheets = getSheetData(sheetFid, token)

        sheets.forEach { sheet ->
            val rsp = getAllCell(sheetFid, sheet.id, token)
            val data = rsp["data"] as Map<*, *>
            val valueRange = data["valueRange"] as Map<*, *>
            val values = valueRange["values"] as? List<*> ?: return@forEach

            @Suppress("UNCHECKED_CAST")
            val stringValues = values as List<List<String>>

            if (stringValues.isEmpty() || stringValues[0].size <= 5 || stringValues[0][5].isBlank()) {
                println("获取不到事件ID,请检查 data: $values")
                return@forEach
            }

            val eventType = stringValues[0][5].split(":")[1].split("\n")[0].replace("$", "").trim()
            val columnHeaders = stringValues[3]

            val filePath = "$fileDir$fileName"
            val file = File(project.rootDir, filePath)

            if (!file.exists()) {
                throw GradleException("目标文件不存在: $filePath")
            }

            val originalContent = file.readText().replace(" ", "").replace("\"", "")

            var kotlinCode = ""
            var isNew = false

            // 处理数据行（从第6行开始，索引为5）
            for (i in 5 until stringValues.size) {
                val rowData = stringValues[i]

                // 构建字段映射
                val headerIndex = mutableMapOf<String, Int?>()
                listOf("exclusive_id", "\$title", "business_type", "\$element_content", "备注").forEach { header ->
                    headerIndex[header] = if (columnHeaders.contains(header)) columnHeaders.indexOf(header) else null
                }

                // 构建字段值
                val fieldValues = mutableMapOf<String, String>()
                headerIndex.forEach { (header, index) ->
                    fieldValues[header] = if (index != null && index < rowData.size) rowData[index] else ""
                }
                fieldValues["version"] = if (rowData.isNotEmpty()) rowData[0] else ""
                fieldValues["event_type"] = eventType

                val exclusiveCode = "TrackParamKey.exclusive_id:${fieldValues["exclusive_id"]}"
                if (originalContent.contains(exclusiveCode)) {
                    println("${fieldValues["exclusive_id"]} 旧版本已经存在,请检查")
                } else {
                    isNew = true
                    kotlinCode += "\n" + generateKotlinMethod(fieldValues)
                }
            }

            kotlinCode += "\n"

            if (isNew) {
                println("完成:$eventType\n")
                file.appendText(kotlinCode)
            } else {
                println("完成:$eventType,没有更新\n")
            }
        }

        println("\n执行完成==============")
    }

    /**
     * 刷新token
     */
    private fun refreshToken(appid: String, appsecret: String): String? {
        val client = HttpClient.newHttpClient()
        val url = "https://open.larksuite.com/open-apis/auth/v3/tenant_access_token/internal"
        val requestBody = """{"app_id": "$appid", "app_secret": "$appsecret"}"""

        val request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Content-Type", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(requestBody))
            .build()

        val response = client.send(request, HttpResponse.BodyHandlers.ofString())
        val jsonSlurper = JsonSlurper()
        val respJson = jsonSlurper.parseText(response.body()) as Map<*, *>

        return if (respJson["code"] == 0) {
            val token = respJson["tenant_access_token"] as String
            println("获取到token: $token")
            token
        } else {
            println("获取token失败: $respJson")
            null
        }
    }

    /**
     * 获取sheet数据
     */
    private fun getSheetData(sheetFid: String, sheetToken: String): List<Sheet> {
        val client = HttpClient.newHttpClient()
        val url = "https://open.larksuite.com/open-apis/sheets/v3/spreadsheets/$sheetFid/sheets/query"

        val request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Authorization", "Bearer $sheetToken")
            .GET()
            .build()

        val response = client.send(request, HttpResponse.BodyHandlers.ofString())

        if (response.statusCode() != 200) {
            throw RuntimeException("Request failed with status code ${response.statusCode()}: ${response.body()}")
        }

        val jsonSlurper = JsonSlurper()
        val respJson = jsonSlurper.parseText(response.body()) as Map<*, *>
        val data = respJson["data"] as Map<*, *>
        @Suppress("UNCHECKED_CAST")
        val sheets = data["sheets"] as List<Map<*, *>>

        return sheets.map { item ->
            Sheet(item["title"] as String, item["sheet_id"] as String)
        }
    }

    /**
     * 生成Kotlin方法
     */
    private fun generateKotlinMethod(info: Map<String, String>): String {
        val exclusiveId = info["exclusive_id"] ?: ""
        val title = info["\$title"]
        val businessType = info["business_type"]
        val elementContent = info["\$element_content"]
        val eventType = info["event_type"] ?: ""

        val params = mutableMapOf("setExclusiveId" to exclusiveId)

        if (!title.isNullOrBlank()) {
            params["setTitle"] = title.trim()
        }

        if (!businessType.isNullOrBlank()) {
            params["setBusinessType"] = businessType.trim()
        }

        if (!elementContent.isNullOrBlank()) {
            params["setElementContent"] = elementContent.trim()
        }

        val reportType = mapOf(
            "ResultBack" to "TrackConstant.EVENT_RESULT_BACK",
            "AppViewScreen" to "TrackConstant.EVENT_APP_VIEW_SCREEN",
            "ViewScreen" to "TrackConstant.EVENT_VIEW_SCREEN",
            "AppClick" to "TrackConstant.EVENT_APP_CLICK",
            "ElementExposure" to "TrackConstant.EVENT_ELEMENT_EXPOSURE"
        )

        val paramsStr = params.map { (k, v) -> ".$k(\"$v\")" }.joinToString("")
        val reportTypeValue = reportType[eventType] ?: "TrackConstant.EVENT_APP_CLICK"

        return """
fun report$exclusiveId(block: (LZTrackInfoBuilder.() -> Unit)? = null) {
    val builder = LZTrackInfoBuilder($reportTypeValue)
        $paramsStr
    block?.invoke(builder)
    reportLizhiPoint(builder.build())
}
    """
    }

    /**
     * 获取所有单元格数据
     */
    private fun getAllCell(sheetFid: String, cellId: String, sheetToken: String): Map<*, *> {
        val client = HttpClient.newHttpClient()
        val url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/$sheetFid/values/$cellId?valueRenderOption=ToString&dateTimeRenderOption=FormattedString"

        val request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Authorization", "Bearer $sheetToken")
            .GET()
            .build()

        val response = client.send(request, HttpResponse.BodyHandlers.ofString())

        if (response.statusCode() != 200) {
            throw RuntimeException("Request failed with status code: ${response.statusCode()}")
        }

        val jsonSlurper = JsonSlurper()
        return jsonSlurper.parseText(response.body()) as Map<*, *>
    }

    /**
     * 提取sheet_fid
     */
    private fun getSheetFid(inputValue: String): String {
        val pattern = Pattern.compile("(?<=sheets/)[^?/]+")
        val matcher = pattern.matcher(inputValue)

        return if (matcher.find()) {
            matcher.group()
        } else {
            inputValue
        }
    }
}