package com.vocalbeats.scaffold.buildlogic

import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.exclude

internal fun Project.configureLizhiDeviceIdentification() {
    dependencies {
        val deviceIdentification = bizLibs.findLibrary("deviceIdentification").get()
        "implementation"(deviceIdentification){
            exclude(group = "com.android.support")
        }
    }
}

internal fun Project.configureLizhiTracker() {
    dependencies {
        val lizhiTracker = bizLibs.findLibrary("lizhiTracker").get()
        "implementation"(lizhiTracker)
    }
}