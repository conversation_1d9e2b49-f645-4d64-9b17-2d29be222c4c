/*
 * Copyright 2023 The Android Open Source Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       https://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.vocalbeats.scaffold.buildlogic

import org.gradle.api.Project
import org.gradle.api.artifacts.VersionCatalog
import org.gradle.api.artifacts.VersionCatalogsExtension
import org.gradle.kotlin.dsl.getByType
import java.util.Properties

val Project.libs
    get(): VersionCatalog = extensions.getByType<VersionCatalogsExtension>().named("libs")

val Project.bizLibs
    get(): VersionCatalog = extensions.getByType<VersionCatalogsExtension>().named("bizLibs")

/**
 * 获取项目配置属性
 */
private val Project.projectProperties: Properties
    get() {
        val properties = Properties()
        val propertiesFile = rootProject.file("project.properties")
        if (propertiesFile.exists()) {
            properties.load(propertiesFile.inputStream())
        }
        return properties
    }

/**
 * 获取 targetSdk 配置
 */
val Project.targetSdk: Int
    get() = projectProperties.getProperty("targetSdk")?.toIntOrNull() ?: 34
