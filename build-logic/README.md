# Convention Plugins

The `build-logic` folder defines project-specific convention plugins, used to keep a single
source of truth for common module configurations.

This approach is heavily based on
[https://developer.squareup.com/blog/herding-elephants/](https://developer.squareup.com/blog/herding-elephants/)
and
[https://github.com/jjohannes/idiomatic-gradle](https://github.com/jjohannes/idiomatic-gradle).

By setting up convention plugins in `build-logic`, we can avoid duplicated build script setup,
messy `subproject` configurations, without the pitfalls of the `buildSrc` directory.

`build-logic` is an included build, as configured in the root
[`settings.gradle.kts`](../settings.gradle.kts).

Inside `build-logic` is a `convention` module, which defines a set of plugins that all normal
modules can use to configure themselves.

`build-logic` also includes a set of `Kotlin` files used to share logic between plugins themselves,
which is most useful for configuring Android components (libraries vs applications) with shared
code.

These plugins are *additive* and *composable*, and try to only accomplish a single responsibility.
Mo<PERSON><PERSON> can then pick and choose the configurations they need.
If there is one-off logic for a module without shared code, it's preferable to define that directly
in the module's `build.gradle`, as opposed to creating a convention plugin with module-specific
setup.

Current list of convention plugins:

- [`vocalbeats.android.application`](convention/src/main/kotlin/AndroidApplicationConventionPlugin.kt),
  [`vocalbeats.android.library`](convention/src/main/kotlin/AndroidLibraryConventionPlugin.kt),
  [`vocalbeats.android.test`](convention/src/main/kotlin/AndroidTestConventionPlugin.kt):
  Configures common Android and Kotlin options.
- [`vocalbeats.android.application.compose`](convention/src/main/kotlin/AndroidApplicationComposeConventionPlugin.kt),
  [`vocalbeats.android.library.compose`](convention/src/main/kotlin/AndroidLibraryComposeConventionPlugin.kt):
  Configures Jetpack Compose options
- [`ModuleGeneratorConventionPlugin`](convention/src/main/kotlin/ModuleGeneratorConventionPlugin.kt):
  Provides tasks for generating standardized Android modules

## Module Generator Plugin

The `ModuleGeneratorConventionPlugin` provides automated module generation capabilities for the project. It supports creating two types of modules:

### Features

- **Automated Directory Structure**: Creates standardized directory layouts for Android modules
- **Pre-configured Build Files**: Generates `build.gradle.kts` files with appropriate plugin configurations
- **Dynamic Package Names**: Automatically reads package name from `project.properties` file
- **Source Code Templates**: Creates basic source files and test structures
- **Compose Integration**: Feature modules include Compose screen templates with ViewModel integration
- **Settings Integration**: Automatically updates `settings.gradle.kts` to include new modules

### Available Tasks

Creates a core module with:
- Basic Android library configuration
- Coroutines dependency
- Standard source directory structure
- Example module class

#### Generate Feature Module
```bash
./gradlew generateModule
```

Creates a feature module with:
- Android feature library configuration
- Compose library integration
- Coroutines dependency
- Standard source directory structure
- Example Compose screen with preview
- Hilt ViewModel with dependency injection setup

### Module Structure

Generated modules follow this structure:
```
moduleType/moduleName/
├── build.gradle.kts
├── .gitignore
└── src/
    ├── main/kotlin/com/vocalbeats/{moduleType}/{moduleName}/
    ├── test/kotlin/com/vocalbeats/{moduleType}/{moduleName}/
    └── androidTest/kotlin/com/vocalbeats/{moduleType}/{moduleName}/
```

### Usage Examples

```bash
# Create a new core module for networking
./gradlew generateCoreModule -PmoduleName=network

# Create a new feature module for user profile
./gradlew generateFeatureModule -PmoduleName=profile
```

### Dynamic Package Name Support

The plugin automatically reads the base package name from the `project.properties` file in the project root. This ensures that all generated modules use consistent package naming throughout the project.

**Configuration:**
```properties
# project.properties
packageName=com.vocalbeats.scaffold
```

**Generated Package Structure:**
- Core modules: `{packageName}.core.{moduleName}`
- Feature modules: `{packageName}.feature.{moduleName}`

**Example:**
If `packageName=com.example.myapp` and you create a module named `auth`, the generated packages will be:
- Core: `com.example.myapp.core.auth`
- Feature: `com.example.myapp.feature.auth`

The plugin will automatically:
1. Read the package name from `project.properties`
2. Create the module directory structure
3. Generate appropriate configuration files with correct namespaces
4. Create source files with proper package declarations
5. Add the module to `settings.gradle.kts`
6. Provide feedback on successful creation
