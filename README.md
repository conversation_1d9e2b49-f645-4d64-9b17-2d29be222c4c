# VB-Android脚手架，使用请fork
## 架构和依赖管理参考[NowInAndroid](https://github.com/android/nowinandroid)
## 模块设计
- #### startup
  - 启动模块
- #### app
  - 依赖各core和feature模块
  - 启动页 MainActivity
    - 导航到各feature页
- #### build-logic
  - 通用模块依赖管理，提供高可复用性
  - ModuleGeneratorConventionPlugin 快速创建模块脚本
    - 通过脚本快速创建符合项目内规范的子模块 ./gradlew generateCoreModule -PmoduleName=xx
  - AndroidLibraryConventionPlugin 实现通用子模块的基础依赖
    - AndroidFeatureConventionPlugin 实现业务模块的标准依赖
    - AndroidLibraryComposeConventionPlugin 实现支持Compose的子模块相关依赖
    - HiltConventionPlugin 实现支持Hilt的相关依赖
    - ...
- #### core
  - common
    - 工具类+扩展方法
    - 使用git子工程进行管理
  - data
    - 所有数据类Repository
  - model
    - 所有业务model
  - database
    - 所有业务数据库
  - datastore
    - 持久化存储
  - network
    - 网络、接口相关
  - designsystem
    - UI设计规范
    - 基础UI组件
  - ui
    - 业务通用UI页面or组件
- #### feature
  - introduce
    - 引导流程
  - subscribe
    - 订阅流程
  - feedback
    - 反馈
  - pay
    - 支付
  - ...
    - 根据需求增加模板feature
