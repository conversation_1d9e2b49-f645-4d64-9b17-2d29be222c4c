plugins {
    alias(libs.plugins.vocalbeats.android.application)
    alias(libs.plugins.vocalbeats.hilt)
    alias(libs.plugins.kotlin.serialization)
    // TODO yangmulin firebase接入
//    alias(libs.plugins.gms)
}

android {
    namespace = rootProject.ext["packageName"] as String
    compileSdk = rootProject.ext["targetSdk"] as Int

    defaultConfig {
        applicationId = rootProject.ext["applicationId"] as String
        minSdk = rootProject.ext["minSdkVersion"] as Int
        targetSdk = rootProject.ext["targetSdk"] as Int
        versionCode = 1
        versionName = "1.0"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        compose = true
    }
    buildFeatures {
        buildConfig = true
    }
}

configurations.all {
    resolutionStrategy {
        // 强制使用指定版本的guava，解决ListenableFuture冲突
        force(libs.guava)
        // 排除冲突的listenablefuture依赖
        exclude(group = "com.google.guava", module = "listenablefuture")
    }
}


dependencies {
    implementation(projects.core.common)
    implementation(projects.core.ui)
    implementation(projects.core.web)
    implementation(projects.core.network)
    implementation(projects.startup)
    implementation(projects.data)
    implementation(projects.feature.onboarding)
    implementation(projects.feature.settings)
    implementation(projects.feature.purchase)
}