package com.vocalbeats.scaffold

import android.app.Application
import android.content.Context
import com.vocalbeats.scaffold.core.common.ktx.appContext
import com.vocalbeats.scaffold.core.common.ktx.initApplication
import com.vocalbeats.scaffold.startup.AppShellStartupManager
import com.yibasan.lizhifm.sdk.platformtools.ApplicationContext
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class AppShell(): Application() {
    @Inject
    lateinit var startupManager: AppShellStartupManager

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        ApplicationContext.init(base)
        ApplicationContext.setApplication(this)
        appContext = this
    }

    override fun onCreate() {
        super.onCreate()
        val buildType = BuildConfig.BUILD_TYPE
        initApplication(buildType)
        startupManager.startupAppShell()
    }

}