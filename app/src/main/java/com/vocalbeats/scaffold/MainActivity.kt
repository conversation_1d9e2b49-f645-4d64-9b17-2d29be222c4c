package com.vocalbeats.scaffold

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.vocalbeats.scaffold.core.network.monitor.NetworkMonitor
import com.vocalbeats.scaffold.ui.ScaffoldApp
import com.vocalbeats.scaffold.ui.rememberAppState
import com.vocalbeats.scaffold.ui.theme.ScaffoldTheme
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var networkMonitor: NetworkMonitor

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            val appState = rememberAppState(
                networkMonitor = networkMonitor,
            )
            ScaffoldTheme {
                ScaffoldApp(
                    appState = appState
                )
            }
        }
    }
}