package com.vocalbeats.scaffold.ui.home

import androidx.compose.foundation.layout.*
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.vocalbeats.designtoken.DesignColors
import com.vocalbeats.designtoken.DesignFonts

/**
 * <AUTHOR>
 * @date 2025/8/11
 * @desc
 */
@Composable
fun HomeScreen(
    onNavToSettings: () -> Unit = {},
    onNavToOnboarding: () -> Unit = {},
    onNavToPurchase: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        HomeButton(
            text = "Settings",
            onClick = onNavToSettings,
        )

        HomeButton(
            text = "Onboarding",
            onClick = onNavToOnboarding,
        )

        HomeButton(
            text = "Purchase",
            onClick = onNavToPurchase,
        )
    }
}

@Composable
private fun HomeButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Button(
        onClick = onClick,
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = text,
            style = DesignFonts.pjsMedium18(),
            color = DesignColors.white100
        )
    }
}