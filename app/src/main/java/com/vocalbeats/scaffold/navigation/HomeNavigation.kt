package com.vocalbeats.scaffold.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.vocalbeats.scaffold.ui.home.HomeScreen

/**
 * <AUTHOR>
 * @date 2025/8/11
 * @desc
 */

fun NavGraphBuilder.homeScreen(
    onNavToSettings: () -> Unit,
    onNavToOnboarding: () -> Unit,
    onNavToPurchase: () -> Unit,
) {
    composable<AppRoute> {
        HomeScreen(
            onNavToSettings = onNavToSettings,
            onNavToOnboarding = onNavToOnboarding,
            onNavToPurchase = onNavToPurchase,
        )
    }
}
