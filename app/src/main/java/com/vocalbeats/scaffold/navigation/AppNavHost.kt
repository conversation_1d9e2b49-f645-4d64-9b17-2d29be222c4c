package com.vocalbeats.scaffold.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import com.vocalbeats.scaffold.core.common.R
import com.vocalbeats.scaffold.core.common.ktx.asStringCtx
import com.vocalbeats.scaffold.core.web.navigation.navToWebView
import com.vocalbeats.scaffold.core.web.navigation.webViewScreen
import com.vocalbeats.scaffold.data.appconfig.AppConfigString
import com.vocalbeats.scaffold.feature.onboarding.navigation.navToOnboarding
import com.vocalbeats.scaffold.feature.onboarding.navigation.onboardingScreen
import com.vocalbeats.scaffold.feature.purchase.navigation.navToPurchase
import com.vocalbeats.scaffold.feature.purchase.navigation.purchaseScreen
import com.vocalbeats.scaffold.feature.settings.navigation.aboutScreen
import com.vocalbeats.scaffold.feature.settings.navigation.feedbackScreen
import com.vocalbeats.scaffold.feature.settings.navigation.navToAbout
import com.vocalbeats.scaffold.feature.settings.navigation.navToFeedback
import com.vocalbeats.scaffold.feature.settings.navigation.navToSettings
import com.vocalbeats.scaffold.feature.settings.navigation.settingsScreen
import com.vocalbeats.scaffold.ui.AppState

/**
 * <AUTHOR>
 * @date 2025/8/11
 * @desc
 */
@Composable
fun AppNavHost(
    appState: AppState,
    modifier: Modifier = Modifier,
) {
    val navController = appState.navController
    NavHost(
        navController = navController,
        startDestination = AppRoute,
        modifier = modifier,
    ) {
        homeScreen(
            onNavToSettings = navController::navToSettings,
            onNavToOnboarding = navController::navToOnboarding,
            onNavToPurchase = navController::navToPurchase,
        )

        webViewScreen(
            onNavBack = navController::popBackStack
        )

        onboardingScreen(
            onPrivacyPolicyClick = navController::navToPrivacyPolicy,
            onTermsOfServiceClick = navController::navToTermsOfUse,
            onContinueClick = navController::navToSettings
        )

        purchaseScreen()

        feedbackScreen(
            onNavBack = navController::popBackStack
        )

        settingsScreen(
            onNavBack = navController::popBackStack,
            onNavToSubscribe = navController::navToOnboarding,
            onNavToAbout = navController::navToAbout,
            onNavToFeedback = navController::navToFeedback
        )

        aboutScreen(
            onNavBack = navController::popBackStack,
            onNavToPrivacyPolicy = navController::navToPrivacyPolicy,
            onNavToTermsOfUse = navController::navToTermsOfUse
        )

    }
}

private fun NavController.navToPrivacyPolicy() = navToWebView(
    url = AppConfigString.PrivacyUrl,
    title = R.string.about_privacy_policy.asStringCtx(context)
)

private fun NavController.navToTermsOfUse() = navToWebView(
    url = AppConfigString.TermsOfServiceUrl,
    title = R.string.about_terms_of_use.asStringCtx(context)
)