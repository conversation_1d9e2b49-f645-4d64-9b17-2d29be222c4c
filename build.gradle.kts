import java.util.Properties

/*
 * By listing all the plugins used throughout all subprojects in the root project build script, it
 * ensures that the build script classpath remains the same for all projects. This avoids potential
 * problems with mismatching versions of transitive plugin dependencies. A subproject that applies
 * an unlisted plugin will have that plugin and its dependencies _appended_ to the classpath, not
 * replacing pre-existing dependencies.
 */
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.android.test) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.compose) apply false
    alias(libs.plugins.kotlin.serialization) apply false
    alias(libs.plugins.dependencyGuard) apply false
    alias(libs.plugins.gms) apply false
    alias(libs.plugins.hilt) apply false
    alias(libs.plugins.ksp) apply false
    alias(libs.plugins.room) apply false
    alias(libs.plugins.module.graph) apply true // Plugin applied to allow module graph generation
    alias(libs.plugins.vocalbeats.module.generator) apply true
    alias(libs.plugins.vocalbeats.track.code.generator) apply true
    alias(libs.plugins.protobuf) apply false
}

buildscript{
    repositories {
        google()
        mavenCentral()

        maven {
            url = uri("http://maven.lizhi.fm:8081/nexus/content/repositories/releases/")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("http://maven.lizhi.fm:8081/nexus/content/repositories/snapshots/")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("http://maven.lizhi.fm:8081/nexus/content/groups/android_public/")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("http://maven.lizhi.fm:8081/nexus/content/repositories/android_uploadpub/")
            isAllowInsecureProtocol = true
        }
    }

    dependencies {
        classpath(bizLibs.litchi.pods.plugin)
    }
}


// 加载项目属性并暴露给所有子模块
val projectProperties = Properties().apply {
    val file = rootProject.file("project.properties")
    if (file.exists()) {
        load(file.inputStream())
    } else {
        println("Properties file not found!")
    }
}

// 将属性暴露给所有子项目
ext {
    set("projectName", projectProperties.getProperty("projectName", ""))
    set("appName", projectProperties.getProperty("appName", ""))
    set("packageName", projectProperties.getProperty("packageName", ""))
    set("applicationId", projectProperties.getProperty("applicationId", ""))
    set("minSdkVersion", projectProperties.getProperty("minSdkVersion", "21").toInt())
    set("targetSdk", projectProperties.getProperty("targetSdk", "36").toInt())
    set("afKey", projectProperties.getProperty("afKey", ""))
    set("afTemplateId", projectProperties.getProperty("afTemplateId", ""))
    set("channelKey", projectProperties.getProperty("channelKey", ""))
    set("productId", projectProperties.getProperty("productId", ""))
    set("lizhiTrackerReportUrl", projectProperties.getProperty("lizhiTrackerReportUrl", ""))
    set("smAppId", projectProperties.getProperty("smAppId", ""))
    set("flashAppId", projectProperties.getProperty("flashAppId", ""))
    set("idlHost",projectProperties.getProperty("idlHost",""))
    set("idlPort",projectProperties.getProperty("idlPort","").toInt())

}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}