# built application files
*.apk
*.ap_

# files for the dex VM
*.dex

# Java class files
*.class

# generated files
bin/
gen/
out/
build/
generated/

# Local configuration file (sdk path, etc)
local.properties

# Eclipse project files
.classpath
.project

# Windows thumbnail db
.DS_Store

# IDEA/Android Studio project files, because
# the project can be imported from settings.gradle.kts
*.iml
.idea/*
!.idea/copyright
# Keep the code styles.
!/.idea/codeStyles
/.idea/codeStyles/*
!/.idea/codeStyles/Project.xml
!/.idea/codeStyles/codeStyleConfig.xml

# Gradle cache
.gradle

# Sandbox stuff
_sandbox

# Android Studio captures folder
captures/

# Kotlin
.kotlin
