# 埋点代码生成器

## 概述

这个Convention Plugin用于从飞书文档生成Android埋点代码，替代了原有的Python脚本。它完全集成到项目的构建系统中，能够从飞书在线文档读取埋点配置，并自动生成对应的Kotlin代码。

## 功能特性

- 🔗 从飞书文档读取埋点配置数据
- 🔄 自动生成Kotlin埋点方法
- ✅ 检查重复埋点避免重复生成
- 📝 支持多种事件类型（AppClick、ViewScreen、ElementExposure等）
- 🎯 智能参数映射和代码格式化
- 🚀 无需Python环境，完全使用Kotlin编写
- 🔧 符合项目Convention Plugin架构
- ⚡ 类型安全的Kotlin实现

## 使用方法

### 1. 基本用法

```bash
./gradlew generateTrackCode -PsheetUrl="https://vocalbeats.sg.larksuite.com/sheets/NTb2s3GwChPOGStoscel7bcLglf?sheet=3COEFS"
```

### 2. 使用Sheet ID

```bash
./gradlew generateTrackCode -PsheetUrl="NTb2s3GwChPOGStoscel7bcLglf"
```

## 配置说明

### Plugin配置

插件会自动从`project.properties`读取配置并动态生成文件路径：

```kotlin
// 自动读取packageName并构建路径
val packageName = project.rootProject.extensions.extraProperties.get("packageName") as String
val packagePath = packageName.replace(".", "/")
val fileDir = "core/common/src/main/java/$packagePath/core/common/track/"
val fileName = "AppTracker.kt"

// 飞书应用配置（可在插件中修改）
val appid = "cli_a5222bd7e378d010_"
val appsecret = "kO7P013s41buBHj5ZAgRThsBtCEICvg3"
```

### 动态路径配置

插件会根据`project.properties`中的`packageName`自动构建正确的文件路径：

- **配置文件**：`project.properties`
- **关键属性**：`packageName=com.vocalbeats.scaffold`
- **生成路径**：`core/common/src/main/java/com/vocalbeats/scaffold/core/common/track/AppTracker.kt`

这意味着当你修改项目的包名时，插件会自动适配新的路径，无需手动修改代码。

#### 示例

假设修改包名为`com.example.myapp`：

1. **修改配置**：
   ```properties
   # project.properties
   packageName=com.example.myapp
   ```

2. **自动生成路径**：
   ```
   core/common/src/main/java/com/example/myapp/core/common/track/AppTracker.kt
   ```

插件会自动处理包名到路径的转换（将`.`替换为`/`）。

### 插件集成

该插件已经集成到项目的build-logic模块中：
- 插件ID：`vocalbeats.track.code.generator` 
- 实现类：`TrackCodeGeneratorConventionPlugin`
- 任务类：`GenerateTrackCodeTask`

### 权限要求

1. 确保机器人已被拉入包含埋点文档的群组
2. 机器人具有读取文档的权限
3. 目标文件 `AppTracker.kt` 必须已存在

## 支持的事件类型

脚本支持以下埋点事件类型：

| 事件类型 | 常量 | 描述 |
|---------|------|------|
| AppClick | EVENT_APP_CLICK | 应用点击事件 |
| ViewScreen | EVENT_VIEW_SCREEN | 页面浏览事件 |
| AppViewScreen | EVENT_APP_VIEW_SCREEN | 应用页面浏览事件 |
| ElementExposure | EVENT_ELEMENT_EXPOSURE | 元素曝光事件 |
| ResultBack | EVENT_RESULT_BACK | 结果回调事件 |

## 生成的代码示例

脚本会根据飞书文档中的配置生成如下格式的Kotlin代码：

```kotlin
fun reportExampleEvent(block: (LZTrackInfoBuilder.() -> Unit)? = null) {
    val builder = LZTrackInfoBuilder(TrackConstant.EVENT_APP_CLICK)
        .setExclusiveId("example_event_001")
        .setTitle("示例页面")
        .setBusinessType("用户行为")
        .setElementContent("按钮点击")
    block?.invoke(builder)
    reportLizhiPoint(builder.build())
}
```

## 文档格式要求

飞书文档需要遵循以下格式：

1. **第1行第6列**：包含事件类型信息（格式：`事件名称:EventType`）
2. **第4行**：列头信息，必须包含：
   - `exclusive_id`：唯一标识符
   - `$title`：标题
   - `business_type`：业务类型
   - `$element_content`：元素内容
   - `备注`：备注信息
3. **第6行开始**：具体的埋点数据

## 架构设计

### Convention Plugin架构优势

1. **类型安全**：使用Kotlin编写，提供编译时类型检查
2. **项目集成**：完全集成到build-logic模块中
3. **标准化**：遵循项目Convention Plugin规范
4. **可维护性**：代码结构清晰，易于维护和扩展

### 代码结构

```
TrackCodeGeneratorConventionPlugin
├── Plugin<Project> 实现
└── GenerateTrackCodeTask 
    ├── 配置变量定义
    ├── refreshToken() 方法
    ├── getSheetData() 方法  
    ├── generateKotlinMethod() 方法
    ├── getAllCell() 方法
    ├── getSheetFid() 方法
    └── @TaskAction generateTrackCode()
```

## 错误处理

### 常见错误及解决方法

1. **获取token失败**
   - 检查appid和appsecret是否正确
   - 确认网络连接正常

2. **获取不到事件ID**
   - 检查文档格式是否正确
   - 确认第1行第6列是否包含事件类型信息

3. **目标文件不存在**
   - 确认 `AppTracker.kt` 文件路径正确
   - 检查文件是否已创建

4. **重复埋点检测**
   - 脚本会自动检测已存在的埋点
   - 只有新的埋点才会被添加到文件中

## 与Python脚本的差异

| 特性 | Python脚本 | Convention Plugin |
|------|-----------|------------------|
| 运行环境 | 需要Python环境 | 使用项目现有Gradle环境 |
| 依赖管理 | 需要安装requests库 | 使用Java内置HTTP客户端 |
| 语言类型 | 动态类型Python | 静态类型Kotlin |
| 函数定义 | def函数 | Kotlin方法 |
| 集成度 | 独立脚本 | Convention Plugin架构 |
| 类型安全 | 运行时检查 | 编译时类型检查 |
| 项目规范 | 独立实现 | 遵循项目架构规范 |

## 技术细节

### HTTP客户端

使用Java 11+内置的HTTP客户端：
- `HttpClient.newHttpClient()`
- `HttpRequest.newBuilder()`
- `HttpResponse.BodyHandlers.ofString()`

### JSON处理

使用Groovy内置的JsonSlurper：
```kotlin
val jsonSlurper = JsonSlurper()
val respJson = jsonSlurper.parseText(response.body()) as Map<*, *>
```

### 字符串处理

使用Java正则表达式和Kotlin字符串模板：
```kotlin
val pattern = Pattern.compile("(?<=sheets/)[^?/]+")
val requestBody = """{"app_id": "$appid", "app_secret": "$appsecret"}"""
```

### 数据类定义

使用Kotlin数据类提供类型安全：
```kotlin
data class Sheet(
    val title: String,
    val id: String
)
```

## 疑难解答

如果遇到问题，请检查：

1. **网络连接**：确保能访问飞书API
2. **权限配置**：确认机器人权限正确
3. **文档格式**：检查飞书文档格式是否符合要求
4. **文件路径**：确认目标文件路径正确
5. **Gradle版本**：确保Gradle版本支持Java 11+ HTTP客户端

### 调试技巧

1. 添加更多日志输出来跟踪执行流程
2. 使用 `--stacktrace` 参数获取详细错误信息
3. 检查生成的HTTP请求内容
4. 验证飞书API响应格式

## 扩展开发

### 自定义配置

可以通过Gradle属性传递自定义配置：

```bash
./gradlew generateTrackCode -PsheetUrl="xxx" -PcustomFileDir="自定义路径"
```

### 添加新事件类型

在 `reportType` 和 `codeRefMap` 映射中添加新的事件类型。

### 支持多文件输出

修改脚本逻辑支持将不同类型的埋点生成到不同文件中。

更多技术支持，请联系开发团队。