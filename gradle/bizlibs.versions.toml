[versions]
designToken = "0.0.1"
appsflyer = "6.12.5"
deviceIdentification = "*******"
lizhiTracker = "2.2.3"
litchiPodsPlugin = "1.6.2"
webView = "2.1.6"


[plugins]
litchi-pods = { id = "com.lizhi.component.pods" }


[libraries]
designToken = { module = "com.vocalbeats.designToken:aitryon-designToken", version.ref = "designToken" }
appsflyer = { group = "com.appsflyer", name = "af-android-sdk", version.ref = "appsflyer" }
deviceIdentification = { group = "com.yibasan.lizhifm.deviceidentification", name = "deviceidentification-lib", version.ref = "deviceIdentification" }
lizhiTracker = { group = "com.yibasan.lizhi.tracker", name = "tracker-lib", version.ref = "lizhiTracker" }
litchi-pods-plugin = { module = "com.lizhi.component.pods:litchi-pods-plugin", version.ref = "litchiPodsPlugin" }
webView = { module = "com.yibasan.lizhifm.sdk.webview:webview-lib", version.ref = "webView" }
webViewJsbridge = { module = "com.lizhi.component.lib:webview-jsbridge-lib", version.ref = "webView" }