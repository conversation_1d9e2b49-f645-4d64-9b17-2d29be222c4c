package com.vocalbeats.scaffold.startup.task.main

import android.app.Application
import com.lizhi.component.basetool.env.Environments
import com.vocalbeats.scaffold.core.common.constants.channelId
import com.vocalbeats.scaffold.core.common.constants.deviceId
import com.vocalbeats.scaffold.core.common.ktx.Logger
import com.vocalbeats.scaffold.core.common.ktx.appLogger
import com.vocalbeats.scaffold.core.common.ktx.log
import com.vocalbeats.scaffold.startup.BuildConfig
import com.vocalbeats.scaffold.startup.task.Task
import com.yibasan.lizhifm.lzlogan.Logz
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class LogInitTask @Inject constructor(private val application: Application) : Task(application, "LogInitTask") {
    private val TAG = "LogInitTask"

    override fun run(application: Application) {
        appLogger = object : Logger {
            override fun log(level: Int, tag: String, msg: String?, t: Throwable?, vararg args: Any?) {
                if (t == null) {
                    Logz.tag(tag).log(level, msg, args)
                } else {
                    Logz.tag(tag).log(level, t, msg, args)
                }
            }
        }

        Logz.init(application, BuildConfig.flashAppId, deviceId, channelId)
        Environments.setFlashDebugMode(true)
        log(TAG, "LoganTask init,flashAppId = ${BuildConfig.flashAppId}")
    }

}