package com.vocalbeats.scaffold.startup

import android.app.Application
import com.vocalbeats.scaffold.core.common.ktx.logInfo
import com.vocalbeats.scaffold.startup.task.Task
import com.vocalbeats.scaffold.startup.task.delay.SMInitTask
import com.vocalbeats.scaffold.startup.task.main.LZTrackerTask
import com.vocalbeats.scaffold.startup.task.main.LogInitTask
import com.vocalbeats.scaffold.startup.task.secondary.AppsflyerTask
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import javax.inject.Inject

class AppShellStartupManager @Inject constructor(
    private val application: Application,
    private val appsflyerTask: AppsflyerTask,
    private val lzTrackerTask: LZTrackerTask,
    private val logInitTask: LogInitTask,
    private val smInitTask: SMInitTask
) {
    private val TAG = "AppShellStartupManager"

    fun startupAppShell() {
        startMainThreadTask()
        startOtherThreadTask()
    }
    private fun startMainThreadTask() {
        mainThreadTasks().forEach {
            it.start()
        }
    }
    private fun startOtherThreadTask() {
        GlobalScope.launch(Dispatchers.IO) {
            otherThreadTasks().forEach {
                it.start()
            }
        }
    }

    /**
     * 需要在主线程执行的任务
     * 这里是主线程串行执行，需要严格按照顺序add
     */
    private fun mainThreadTasks() : List<Task> {
        return ArrayList<Task>().apply {
            add(logInitTask)
            add(lzTrackerTask)
        }
    }

    /**
     * 子线程执行的任务
     * 也是串行执行，需要严格按照顺序add
     */
    private fun otherThreadTasks() : List<Task> {
        return ArrayList<Task>().apply {
            add(appsflyerTask)
            add(smInitTask)
        }
    }

}