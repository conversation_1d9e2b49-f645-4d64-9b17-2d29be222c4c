package com.vocalbeats.scaffold.startup.task.main

import android.app.Application
import com.sensorsdata.analytics.android.sdk.SAConfigOptions
import com.sensorsdata.analytics.android.sdk.SensorsAnalyticsAutoTrackEventType
import com.vocalbeats.scaffold.core.common.ktx.appName
import com.vocalbeats.scaffold.core.common.ktx.isAppInForeground
import com.vocalbeats.scaffold.core.common.ktx.isDebug
import com.vocalbeats.scaffold.core.common.ktx.isReleaseLogBuildType
import com.vocalbeats.scaffold.core.common.ktx.logInfo
import com.vocalbeats.scaffold.startup.BuildConfig
import com.vocalbeats.scaffold.startup.task.Task
import com.yibasan.lizhi.tracker.LZTracker
import com.yibasan.lizhi.tracker.config.BUILD_DEBUG
import com.yibasan.lizhi.tracker.config.BUILD_RELEASE
import com.yibasan.lizhi.tracker.config.TrackOptions
import org.json.JSONObject
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class LZTrackerTask @Inject constructor(private val application: Application) : Task(application, "LZTrackerTask") {

    companion object {
        const val TASK_TAG = "LZTrackerTask"
    }

    override fun run(application: Application) {
        //buildType = 1 为测试环境, 0或者空为市场包
        val buildType = if (isDebug || isReleaseLogBuildType) BUILD_DEBUG else BUILD_RELEASE
        //市场投放/应用商店渠道包标识appkey，根据渠道切换appKey
        val marketAppKey = BuildConfig.channelKey
        val productId = BuildConfig.productId
        logInfo(TASK_TAG, "LZTracker.init appkey = $marketAppKey, productId = $productId")
        LZTracker.init(
            context = application,
            options = TrackOptions(
                productId = productId,
                appName = appName,
                appKey = marketAppKey,
                buildType = buildType
            ),
            saConfig = getSAConfigOptions()
        )
        LZTracker.registerProperties(JSONObject().apply {
            put("platform_type", "Android")
        })
        LZTracker.registerDynamicProperties {
            JSONObject().apply {
                put("customized_app_running_background", if (isAppInForeground.not()) 1 else 0)
            }
        }
    }

    private fun getSAConfigOptions(): SAConfigOptions {
        val saConfigOptions = SAConfigOptions(BuildConfig.lizhiTrackerReportUrl)
        saConfigOptions.setAutoTrackEventType(
            SensorsAnalyticsAutoTrackEventType.APP_CLICK or
                    SensorsAnalyticsAutoTrackEventType.APP_START or
                    SensorsAnalyticsAutoTrackEventType.APP_END or
                    SensorsAnalyticsAutoTrackEventType.APP_VIEW_SCREEN
        )
        //配置 SDK，如开启全埋点、开启 Log 等
        saConfigOptions.enableLog(isDebug)
        return saConfigOptions
    }
}