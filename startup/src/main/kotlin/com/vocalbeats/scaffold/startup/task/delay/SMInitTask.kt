package com.vocalbeats.scaffold.startup.task.delay

import android.app.Application
import com.ishumei.smantifraud.SmAntiFraud
import com.ishumei.smantifraud.SmAntiFraud.SmOption
import com.vocalbeats.scaffold.startup.BuildConfig
import com.vocalbeats.scaffold.startup.task.Task
import com.yibasan.lizhifm.lzlogan.Logz
import javax.inject.Inject
import javax.inject.Singleton

/**
 * @Desc: 数美风控sdk初始化
 * 注意: 需要在主进程初始化
 */
@Singleton
class SMInitTask @Inject constructor(private val application: Application) : Task(application, "SMInitTask"){
    private val TAG = "SMInitTask"
    private val publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCipeRFFLohwHtGUKyOlTh4MmbcJvZlB2wK9xU8K2ulNd1+NVXgmj2mG8RF2wVAsCAK1bGWwWRrqgg0GR/P9wagtTFdzXNlfJplewnvgxEr/FuefuoOHoCWBREHrgpThDmneZ8XRtF9yJYY9aRJgfvTFoebW0mibWS3MEoUN09nQwIDAQAB"
    private val smAppId = BuildConfig.smAppId

    override fun run(application: Application) {
        try {
            init()
        } catch (e: Exception) {
            Logz.tag(TAG).e(e.message)
        }
    }

    private fun init() {
        Logz.tag(TAG).d("SmAntiFraud sdkVersion = ${SmAntiFraud.getSDKVersion()}")
        val option = SmOption()
        val url = "https://tycollectproxy.tiyalive.com"
        option.url = "$url/$smAppId/v3/profile/android"
        option.contactUrl = "$url/$smAppId/v3/profile/android"
        option.confUrl = "$url/$smAppId/v3/cloudconf"
        option.traceUrl = "$url/$smAppId/v3/tracker?os=android"
        option.isCheckCrt = false
        // 必填，修改为自己的organization
        option.organization = "2OaFzWJr4Iq43hwimOdh"
        //必填，此处填写android.cer 文件内容
        option.publicKey = publicKey
        SmAntiFraud.create(application, option)
        Logz.tag(TAG).d("SMInitTask init success")
    }

}