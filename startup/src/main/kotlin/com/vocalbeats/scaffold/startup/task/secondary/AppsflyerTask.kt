package com.vocalbeats.scaffold.startup.task.secondary

import android.app.Application
import com.appsflyer.AppsFlyerConversionListener
import com.appsflyer.AppsFlyerLib
import com.vocalbeats.scaffold.core.common.constants.channelId
import com.vocalbeats.scaffold.core.common.constants.deviceId
import com.vocalbeats.scaffold.core.common.ktx.logInfo
import com.vocalbeats.scaffold.data.model.AppsFlyersData
import com.vocalbeats.scaffold.data.repository.AppsFlyersRepository
import com.vocalbeats.scaffold.startup.BuildConfig
import com.vocalbeats.scaffold.startup.task.Task
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AppsflyerTask @Inject constructor(
    private val application: Application,
    private val appsflyerRepository: AppsFlyersRepository
) : Task(application, "AppsflyerTask") {
    private val TAG = "AppsflyerTask"
    //当前只会上google


    override fun run(application: Application) {
        logInfo(TAG,"run: on appsflyer task deviceId = $deviceId")

        AppsFlyerLib.getInstance().setCustomerUserId(deviceId)
        AppsFlyerLib.getInstance().setCollectOaid(true)
//        AppsFlyerLib.getInstance().setOneLinkCustomDomain("invite.buz-app.com")
        if (BuildConfig.afTemplateId.isNotEmpty()){
            AppsFlyerLib.getInstance().setAppInviteOneLink(BuildConfig.afTemplateId)
        }
        AppsFlyerLib.getInstance().setOutOfStore(channelId)

        AppsFlyerLib.getInstance().init(
            BuildConfig.afKey, object : AppsFlyerConversionListener {
                override fun onConversionDataSuccess(map: MutableMap<String, Any>?) {
                    val mediaSource = map?.get("media_source")
                    // TODO yangmulin 待确认mediaSource是否需要使用

                    val isFirstLaunch = map?.get("is_first_launch")?:false
                    logInfo(TAG,"onConversionDataSuccess isFirstLaunch = $isFirstLaunch")
                    if (!(isFirstLaunch as Boolean)) return

                    val deepLink = map?.get("deep_link_sub1") as? String?
                    logInfo(TAG,"onConversionDataSuccess deep_link_sub1 = $deepLink")

                    val campaign = map?.get("campaign") as? String?
                    logInfo(TAG,"onConversionDataSuccess: campaign = $campaign")
                    appsflyerRepository.onAppsFlyerCallBack(
                        AppsFlyersData(
                            true,
                            deepLink,
                            campaign
                        )
                    )
                }

                override fun onConversionDataFail(msg: String?) {
                }

                override fun onAppOpenAttribution(map: MutableMap<String, String>) {
                    logInfo(TAG,"AppsFlyerConversionListener onAppOpenAttribution: $map")
                    val deepLink = map["deep_link_sub1"]
                    logInfo(TAG,"onAppOpenAttribution deep_link_sub1 = $deepLink")
                    appsflyerRepository.onAppsFlyerCallBack(
                        AppsFlyersData(
                            false,
                            deepLink,
                            null
                        )
                    )
                }

                override fun onAttributionFailure(p0: String?) {
                }
            },
            application
        )
        AppsFlyerLib.getInstance().start(application, BuildConfig.afKey)
    }
}