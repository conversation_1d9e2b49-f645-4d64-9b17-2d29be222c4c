#!/bin/bash

set -e

# 项目根目录
PROJECT_ROOT=$(pwd)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}===========================================${NC}"
echo -e "${BLUE}      Android项目包结构重构脚本 ${NC}"
echo -e "${BLUE}===========================================${NC}"

# 函数：输出带颜色的消息
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：检查是否在正确的目录
check_project_structure() {
    if [ ! -f "project.properties" ] || [ ! -f "settings.gradle.kts" ]; then
        log_error "请在Android项目根目录下运行此脚本！"
        exit 1
    fi
}

# 函数：获取包名配置
get_package_name() {
    if [ -f "project.properties" ]; then
        PACKAGE_NAME=$(grep "^packageName=" project.properties | cut -d'=' -f2)
        if [ -z "$PACKAGE_NAME" ]; then
            log_error "无法从project.properties文件中读取packageName"
            exit 1
        fi
        log_info "检测到目标包名: $PACKAGE_NAME"
    else
        log_error "未找到project.properties文件"
        exit 1
    fi
}

# 函数：创建临时工作目录
create_temp_dir() {
    TEMP_DIR="/tmp/refactor_$$"
    mkdir -p "$TEMP_DIR"
    log_info "创建临时目录: $TEMP_DIR"
}

# 函数：清理临时目录
cleanup_temp_dir() {
    rm -rf "$TEMP_DIR"
    log_info "清理临时目录"
}

# 函数：扫描需要重构的包
scan_packages() {
    log_info "扫描需要重构的包..."
    
    # 查找所有.kt和.java文件（包含main、androidTest、test目录）
    find "$PROJECT_ROOT" -name "*.kt" -o -name "*.java" | grep -E "(src/(main|androidTest|test)/)" > "$TEMP_DIR/all_files.txt"
    
    # 提取包名
    while read -r file; do
        if [ -f "$file" ]; then
            package_line=$(grep "^package " "$file" | head -1)
            if [ -n "$package_line" ]; then
                package_name=$(echo "$package_line" | sed 's/package //g' | sed 's/;//g' | tr -d ' ')
                
                # 检查是否需要重构 - 包含app模块、core模块和feature模块
                if [[ "$package_name" == com.vocalbeats.scaffold ]] || [[ "$package_name" == com.vocalbeats.scaffold.* ]]; then
                    if [[ "$package_name" != $PACKAGE_NAME ]] && [[ "$package_name" != $PACKAGE_NAME.* ]]; then
                        echo "$package_name|$file" >> "$TEMP_DIR/packages_to_refactor.txt"
                    fi
                fi
            fi
        fi
    done < "$TEMP_DIR/all_files.txt"
    
    # 统计需要重构的包
    if [ -f "$TEMP_DIR/packages_to_refactor.txt" ]; then
        local package_count=$(cut -d'|' -f1 "$TEMP_DIR/packages_to_refactor.txt" | sort | uniq | wc -l)
        local file_count=$(wc -l < "$TEMP_DIR/packages_to_refactor.txt")
        log_info "发现 $package_count 个包需要重构，涉及 $file_count 个文件"
        
        # 显示需要重构的包
        cut -d'|' -f1 "$TEMP_DIR/packages_to_refactor.txt" | sort | uniq > "$TEMP_DIR/unique_packages.txt"
        log_info "需要重构的包:"
        while read -r pkg; do
            log_info "  - $pkg"
        done < "$TEMP_DIR/unique_packages.txt"
    else
        log_warning "没有找到需要重构的包"
        return 1
    fi
}

# 函数：重构单个包
refactor_single_package() {
    local old_package="$1"
    local new_package=""
    
    # 构建新包名
    if [[ "$old_package" == com.vocalbeats.scaffold.feature.* ]]; then
        new_package="${old_package/com.vocalbeats.scaffold.feature/$PACKAGE_NAME.feature}"
    elif [[ "$old_package" == com.vocalbeats.scaffold.core.* ]]; then
        new_package="${old_package/com.vocalbeats.scaffold.core/$PACKAGE_NAME.core}"
    elif [[ "$old_package" == com.vocalbeats.scaffold ]]; then
        new_package="$PACKAGE_NAME"
    elif [[ "$old_package" == com.vocalbeats.scaffold.* ]]; then
        # 处理app模块下的子包，如com.vocalbeats.scaffold.ui.theme
        new_package="${old_package/com.vocalbeats.scaffold/$PACKAGE_NAME}"
    else
        log_warning "不支持的包格式: $old_package"
        return 1
    fi
    
    log_info "重构包: $old_package -> $new_package"
    
    # 构建目录路径
    local old_path="${old_package//.//}"
    local new_path="${new_package//.//}"
    
    # 查找该包所在的所有模块目录
    grep "^$old_package|" "$TEMP_DIR/packages_to_refactor.txt" > "$TEMP_DIR/current_package_files.txt"
    
    # 避免重复处理，只移动一次目录
    local moved=false
    
    while read -r line; do
        local file_path=$(echo "$line" | cut -d'|' -f2)
        local module_src_dir=$(echo "$file_path" | sed 's|/src/main/.*|/src/main|')
        
        # 如果已经移动过，跳过
        [ "$moved" = true ] && continue
        
        # 检查java和kotlin目录
        for src_type in "java" "kotlin"; do
            local src_dir="$module_src_dir/$src_type"
            local old_dir_full="$src_dir/$old_path"
            local new_dir_full="$src_dir/$new_path"
            
            if [ -d "$old_dir_full" ]; then
                log_info "移动目录: $old_dir_full -> $new_dir_full"
                
                # 创建新目录结构
                mkdir -p "$(dirname "$new_dir_full")"
                
                # 移动目录
                if mv "$old_dir_full" "$new_dir_full"; then
                    log_success "✓ 目录移动成功"
                    moved=true
                    
                    # 清理空的父目录
                    local cleanup_dir="$(dirname "$old_dir_full")"
                    while [ "$cleanup_dir" != "$src_dir" ] && [ -d "$cleanup_dir" ] && [ -z "$(ls -A "$cleanup_dir")" ]; do
                        rmdir "$cleanup_dir"
                        cleanup_dir=$(dirname "$cleanup_dir")
                    done
                    
                    # 移动成功后跳出所有循环
                    break 2
                else
                    log_error "目录移动失败: $old_dir_full"
                fi
            fi
        done
    done < "$TEMP_DIR/current_package_files.txt"
    
    # 更新所有相关文件的package声明
    while read -r line; do
        local file_path=$(echo "$line" | cut -d'|' -f2)
        
        if [ -f "$file_path" ]; then
            log_info "更新文件: $(basename "$file_path")"
            
            # 备份文件
            cp "$file_path" "$file_path.backup"
            
            # 更新package声明 (使用纯bash实现，避免sed依赖)
            local temp_file="$file_path.tmp"
            while IFS= read -r line; do
                if [[ "$line" == "package $old_package"* ]]; then
                    printf '%s\n' "package $new_package"
                else
                    printf '%s\n' "$line"
                fi
            done < "$file_path" > "$temp_file"
            mv "$temp_file" "$file_path"
            
            log_success "✓ 文件更新完成"
        fi
    done < "$TEMP_DIR/current_package_files.txt"
}

# 函数：更新import引用
update_all_imports_and_packages() {
    log_info "更新全局import引用和package声明..."

    # 查找所有需要更新的文件
    find "$PROJECT_ROOT" -name "*.kt" -o -name "*.java" | grep -v ".backup" > "$TEMP_DIR/all_import_files.txt"

    while read -r file; do
        local updated=false

        # 备份文件（如果还没备份过）
       if [ ! -f "$file.backup" ]; then
           # 检查是否有需要更新的import、package声明或硬编码包名字符串
           local has_old_import=$(grep -qE "^import com\.vocalbeats\.scaffold(\.|$)" "$file" && echo "true" || echo "false")
           local has_old_package=$(grep -qE "^package com\.vocalbeats\.scaffold(\.|$)" "$file" && echo "true" || echo "false")
           local has_old_string=$(grep -q "\"com\.vocalbeats\.scaffold\"" "$file" && echo "true" || echo "false")

           if [[ "$has_old_import" == "true" ]] || [[ "$has_old_package" == "true" ]] || [[ "$has_old_string" == "true" ]]; then
               cp "$file" "$file.backup"
               updated=true
           fi
       else
           # 如果已有备份，检查是否仍需要更新
           local has_old_import=$(grep -qE "^import com\.vocalbeats\.scaffold(\.|$)" "$file" && echo "true" || echo "false")
           local has_old_package=$(grep -qE "^package com\.vocalbeats\.scaffold(\.|$)" "$file" && echo "true" || echo "false")
           local has_old_string=$(grep -q "\"com\.vocalbeats\.scaffold\"" "$file" && echo "true" || echo "false")

           if [[ "$has_old_import" == "true" ]] || [[ "$has_old_package" == "true" ]] || [[ "$has_old_string" == "true" ]]; then
               updated=true
           fi
       fi

        if [ "$updated" = true ]; then
            # 更新import和package语句 (使用纯bash实现，避免sed依赖)
            local temp_file="$file.tmp"
            {
                while IFS= read -r line || [[ -n "$line" ]]; do
                  if [[ "$line" == *"import com.vocalbeats.scaffold."*".R"* ]]; then
                    printf '%s\n' "${line//com.vocalbeats.scaffold/$PACKAGE_NAME}"
                    # 替换包含com.vocalbeats.scaffold的导入语句，且不包含$PACKAGE_NAME
                  elif [[ "$line" == "import com.vocalbeats.scaffold"* && ! "$line" == *"$PACKAGE_NAME"* ]]; then
                        printf '%s\n' "${line//com.vocalbeats.scaffold/$PACKAGE_NAME}"
                    # 替换包含com.vocalbeats.scaffold的包声明，且不包含$PACKAGE_NAME
                  elif [[ "$line" == "package com.vocalbeats.scaffold"* && ! "$line" == *"$PACKAGE_NAME"* ]]; then
                        printf '%s\n' "${line//com.vocalbeats.scaffold/$PACKAGE_NAME}"
                    # 替换测试代码中的硬编码包名字符串
                  elif [[ "$line" == *"\"com.vocalbeats.scaffold\""* && ! "$line" == *"$PACKAGE_NAME"* ]]; then
                        printf '%s\n' "${line//\"com.vocalbeats.scaffold\"/\"$PACKAGE_NAME\"}"
                  else
                        printf '%s\n' "$line"
                    fi
                done
            } < "$file" > "$temp_file"
            mv "$temp_file" "$file"

            log_info "✓ 更新import和package: $(basename "$file")"
        fi
    done < "$TEMP_DIR/all_import_files.txt"
}

# 主函数
main() {
    # 检查项目结构
    check_project_structure
    
    # 获取包名
    get_package_name
    
    # 创建临时目录
    create_temp_dir
    
    # 确保清理函数在脚本退出时运行
    trap cleanup_temp_dir EXIT
    
    log_info "开始重构包结构..."
    log_info "目标包名前缀: $PACKAGE_NAME"
    echo ""
    
    # 扫描需要重构的包
    if ! scan_packages; then
        log_info "没有需要重构的包，脚本退出"
        exit 0
    fi
    
    echo ""
    
    # 逐个重构包
    while read -r old_package; do
        refactor_single_package "$old_package"
        echo ""
    done < "$TEMP_DIR/unique_packages.txt"
    
    # 更新所有import引用
    update_all_imports_and_packages
    
    # 询问是否删除备份文件
    echo ""

    find "$PROJECT_ROOT" -name "*.backup" -delete
    log_success "已删除所有备份文件"
    
    log_success "包结构重构完成！"
}

# 运行主函数
main "$@"